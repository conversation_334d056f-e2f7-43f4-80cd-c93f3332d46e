INFO 2025-09-08 16:37:17,070 train_utils.py: 126: MACHINE SEED: 3690
INFO 2025-09-08 16:37:17,075 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 16:37:17,076 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=58717
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 16:37:17,078 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:37:17,079 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml/tensorboard
INFO 2025-09-08 16:37:17,480 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 16:37:17,483 trainer.py:1243: ====================
INFO 2025-09-08 16:37:17,484 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 16:37:17,484 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 16:37:17,489 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 16:37:17,489 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 16:37:17,489 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 16:37:17,489 trainer.py:1253: ====================
INFO 2025-09-08 16:37:17,492 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:37:17,492 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:37:17,585 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:37:17,599 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias'}
INFO 2025-09-08 16:37:17,600 optimizer.py: 248: Matches for param_name [*bias*]: {'image_encoder.trunk.blocks.15.attn.proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'memory_attention.layers.1.linear1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'memory_attention.norm.bias', 'memory_encoder.pix_feat_proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.neck.convs.1.conv.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'obj_ptr_tpos_proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.neck.convs.3.conv.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'memory_attention.layers.1.norm3.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'sam_mask_decoder.conv_s0.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'obj_ptr_proj.layers.2.bias', 'memory_attention.layers.0.linear1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'memory_attention.layers.3.linear2.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'memory_attention.layers.1.linear2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'memory_attention.layers.2.linear2.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'obj_ptr_proj.layers.1.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'mask_downsample.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'memory_attention.layers.0.linear2.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'memory_attention.layers.2.linear1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'memory_attention.layers.0.norm3.bias', 'memory_encoder.out_proj.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.3.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'memory_attention.layers.3.linear1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm2.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias'}
INFO 2025-09-08 16:37:17,600 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'memory_attention.layers.0.norm1.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'memory_attention.layers.1.norm3.bias', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'memory_attention.layers.2.norm3.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'memory_attention.layers.0.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'memory_attention.layers.2.norm2.weight', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'memory_attention.layers.0.norm3.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.19.norm1.bias', 'memory_attention.layers.2.norm1.weight', 'memory_attention.layers.1.norm3.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.15.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm2.bias', 'memory_attention.layers.3.norm3.weight', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.9.norm2.weight'} 
INFO 2025-09-08 16:37:18,217 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 16:37:18,217 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 16:37:18,217 train_utils.py: 340: Checkpoint file does not exist: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:18,342 trainer.py: 517: Loading pretrained checkpoint from {'_partial_': True, '_target_': 'training.utils.checkpoint_utils.load_state_dict_into_model', 'strict': True, 'ignore_unexpected_keys': None, 'ignore_missing_keys': None, 'state_dict': {'_target_': 'training.utils.checkpoint_utils.load_checkpoint_and_apply_kernels', 'checkpoint_path': 'checkpoints/sam2.1_hiera_base_plus.pt', 'ckpt_state_dict_keys': ['model']}}
INFO 2025-09-08 16:37:18,400 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 16:37:18,400 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 16:37:18,400 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 16:37:18,400 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 16:37:18,400 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 16:37:18,400 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 16:37:18,521 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 16:37:18,521 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 16:37:18,521 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 16:37:18,521 trainer.py: 261: Trainer initialization completed
INFO 2025-09-08 16:37:24,303 train_utils.py: 317: Train Epoch: [0][0/4] | Batch Time: 5.78 (5.78) | Data Time: 4.49 (4.49) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 3.59e-01 (3.59e-01)
INFO 2025-09-08 16:37:26,464 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 16:37:26,465 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:37:26,466 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7371016070246696, 'Losses/train_all_loss_mask': 0.0032584584841970354, 'Losses/train_all_loss_dice': 0.2737828940153122, 'Losses/train_all_loss_iou': 0.30841244757175446, 'Losses/train_all_loss_class': 0.08973711130056472, 'Losses/train_all_core_loss': 0.7371016070246696, 'Trainer/where': 0.0, 'Trainer/epoch': 0, 'Trainer/steps_train': 4}
INFO 2025-09-08 16:37:26,470 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:26,734 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:31,878 train_utils.py: 317: Train Epoch: [1][0/4] | Batch Time: 5.07 (5.07) | Data Time: 4.37 (4.37) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.62e-01 (2.62e-01)
INFO 2025-09-08 16:37:34,075 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 16:37:34,075 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:37:34,077 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6112263593822718, 'Losses/train_all_loss_mask': 0.004403540342536871, 'Losses/train_all_loss_dice': 0.31714580953121185, 'Losses/train_all_loss_iou': 0.15220089675858617, 'Losses/train_all_loss_class': 0.05380886875354918, 'Losses/train_all_core_loss': 0.6112263593822718, 'Trainer/where': 0.0, 'Trainer/epoch': 1, 'Trainer/steps_train': 8}
INFO 2025-09-08 16:37:34,082 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:34,465 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:39,478 train_utils.py: 317: Train Epoch: [2][0/4] | Batch Time: 4.94 (4.94) | Data Time: 4.28 (4.28) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.00e-01 (2.00e-01)
INFO 2025-09-08 16:37:41,528 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:37:41,528 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:37:41,528 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6553265564143658, 'Losses/train_all_loss_mask': 0.0034441582902218215, 'Losses/train_all_loss_dice': 0.2847025394439697, 'Losses/train_all_loss_iou': 0.15912650059908628, 'Losses/train_all_loss_class': 0.14261432734929258, 'Losses/train_all_core_loss': 0.6553265564143658, 'Trainer/where': 0.0, 'Trainer/epoch': 2, 'Trainer/steps_train': 12}
INFO 2025-09-08 16:37:41,531 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:41,943 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:46,931 train_utils.py: 317: Train Epoch: [3][0/4] | Batch Time: 4.92 (4.92) | Data Time: 4.17 (4.17) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 8.26e-02 (8.26e-02)
INFO 2025-09-08 16:37:48,927 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:37:48,927 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:37:48,927 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5533813647925854, 'Losses/train_all_loss_mask': 0.0006868674427096266, 'Losses/train_all_loss_dice': 0.12461648136377335, 'Losses/train_all_loss_iou': 0.07291077869012952, 'Losses/train_all_loss_class': 0.34211675168626243, 'Losses/train_all_core_loss': 0.5533813647925854, 'Trainer/where': 0.0, 'Trainer/epoch': 3, 'Trainer/steps_train': 16}
INFO 2025-09-08 16:37:48,931 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:49,310 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:54,684 train_utils.py: 317: Train Epoch: [4][0/4] | Batch Time: 5.30 (5.30) | Data Time: 4.36 (4.36) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.26e+00 (1.26e+00)
INFO 2025-09-08 16:37:56,980 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:37:56,980 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:37:56,980 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6950331013649702, 'Losses/train_all_loss_mask': 0.0012995629294891842, 'Losses/train_all_loss_dice': 0.2281598597764969, 'Losses/train_all_loss_iou': 0.21880601393058896, 'Losses/train_all_loss_class': 0.22207597654414712, 'Losses/train_all_core_loss': 0.6950331013649702, 'Trainer/where': 0.0, 'Trainer/epoch': 4, 'Trainer/steps_train': 20}
INFO 2025-09-08 16:37:56,989 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:37:57,430 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:02,476 train_utils.py: 317: Train Epoch: [5][0/4] | Batch Time: 4.97 (4.97) | Data Time: 4.22 (4.22) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.23e-01 (1.23e-01)
INFO 2025-09-08 16:38:04,536 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:04,536 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:04,536 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.21048945374786854, 'Losses/train_all_loss_mask': 0.0008936460071709007, 'Losses/train_all_loss_dice': 0.1202208548784256, 'Losses/train_all_loss_iou': 0.07219724589958787, 'Losses/train_all_loss_class': 0.00019843682093778625, 'Losses/train_all_core_loss': 0.21048945374786854, 'Trainer/where': 0.0, 'Trainer/epoch': 5, 'Trainer/steps_train': 24}
INFO 2025-09-08 16:38:04,546 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:04,976 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:10,101 train_utils.py: 317: Train Epoch: [6][0/4] | Batch Time: 5.05 (5.05) | Data Time: 4.23 (4.23) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.94e+00 (1.94e+00)
INFO 2025-09-08 16:38:12,625 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:12,625 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:12,625 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.1100987941026688, 'Losses/train_all_loss_mask': 0.008136966382153332, 'Losses/train_all_loss_dice': 0.37174442410469055, 'Losses/train_all_loss_iou': 0.19891836494207382, 'Losses/train_all_loss_class': 0.376696680286841, 'Losses/train_all_core_loss': 1.1100987941026688, 'Trainer/where': 0.0, 'Trainer/epoch': 6, 'Trainer/steps_train': 28}
INFO 2025-09-08 16:38:12,631 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:13,025 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:18,616 train_utils.py: 317: Train Epoch: [7][0/4] | Batch Time: 5.52 (5.52) | Data Time: 4.42 (4.42) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.12e+00 (6.12e+00)
INFO 2025-09-08 16:38:20,550 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:20,550 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:20,550 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 2.2520583122968674, 'Losses/train_all_loss_mask': 0.018374109276919626, 'Losses/train_all_loss_dice': 0.9622166380286217, 'Losses/train_all_loss_iou': 0.6167945838533342, 'Losses/train_all_loss_class': 0.3055649737079875, 'Losses/train_all_core_loss': 2.2520583122968674, 'Trainer/where': 0.0, 'Trainer/epoch': 7, 'Trainer/steps_train': 32}
INFO 2025-09-08 16:38:20,559 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:20,973 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:26,449 train_utils.py: 317: Train Epoch: [8][0/4] | Batch Time: 5.40 (5.40) | Data Time: 4.53 (4.53) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.35e+00 (1.35e+00)
INFO 2025-09-08 16:38:28,716 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:28,716 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:28,716 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4955861270427704, 'Losses/train_all_loss_mask': 0.0028858057848992757, 'Losses/train_all_loss_dice': 0.2832159325480461, 'Losses/train_all_loss_iou': 0.12641933094710112, 'Losses/train_all_loss_class': 0.028234747289388906, 'Losses/train_all_core_loss': 0.4955861270427704, 'Trainer/where': 0.0, 'Trainer/epoch': 8, 'Trainer/steps_train': 36}
INFO 2025-09-08 16:38:28,728 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:29,137 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:34,349 train_utils.py: 317: Train Epoch: [9][0/4] | Batch Time: 5.13 (5.13) | Data Time: 4.41 (4.41) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.09e-01 (1.09e-01)
INFO 2025-09-08 16:38:36,953 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:36,954 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:36,954 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.3230420593172312, 'Losses/train_all_loss_mask': 0.0009305895218858495, 'Losses/train_all_loss_dice': 0.14769680798053741, 'Losses/train_all_loss_iou': 0.09612469701096416, 'Losses/train_all_loss_class': 0.06060877438630996, 'Losses/train_all_core_loss': 0.3230420593172312, 'Trainer/where': 0.0, 'Trainer/epoch': 9, 'Trainer/steps_train': 40}
INFO 2025-09-08 16:38:36,962 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:37,486 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:42,656 train_utils.py: 317: Train Epoch: [10][0/4] | Batch Time: 5.09 (5.09) | Data Time: 4.37 (4.37) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.36e-01 (6.36e-01)
INFO 2025-09-08 16:38:44,552 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:44,552 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:44,552 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2431792076677084, 'Losses/train_all_loss_mask': 0.00039915504752467256, 'Losses/train_all_loss_dice': 0.05303352326154709, 'Losses/train_all_loss_iou': 0.02757557574659586, 'Losses/train_all_loss_class': 0.1545870064474002, 'Losses/train_all_core_loss': 0.2431792076677084, 'Trainer/where': 0.0, 'Trainer/epoch': 10, 'Trainer/steps_train': 44}
INFO 2025-09-08 16:38:44,565 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:45,292 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:51,507 train_utils.py: 317: Train Epoch: [11][0/4] | Batch Time: 6.01 (6.01) | Data Time: 5.30 (5.30) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.04e-01 (2.04e-01)
INFO 2025-09-08 16:38:54,217 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:38:54,217 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:38:54,217 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.22843365743756294, 'Losses/train_all_loss_mask': 0.0007070374813338276, 'Losses/train_all_loss_dice': 0.0874171331524849, 'Losses/train_all_loss_iou': 0.04141010041348636, 'Losses/train_all_loss_class': 0.08546568097153795, 'Losses/train_all_core_loss': 0.22843365743756294, 'Trainer/where': 0.0, 'Trainer/epoch': 11, 'Trainer/steps_train': 48}
INFO 2025-09-08 16:38:54,228 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:38:54,820 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:01,098 train_utils.py: 317: Train Epoch: [12][0/4] | Batch Time: 6.16 (6.16) | Data Time: 5.26 (5.26) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 7.83e-02 (7.83e-02)
INFO 2025-09-08 16:39:04,947 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:39:04,947 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:04,947 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.8673592787235975, 'Losses/train_all_loss_mask': 0.004250451107509434, 'Losses/train_all_loss_dice': 0.41867728531360626, 'Losses/train_all_loss_iou': 0.266520572360605, 'Losses/train_all_loss_class': 0.09715231717564166, 'Losses/train_all_core_loss': 0.8673592787235975, 'Trainer/where': 0.0, 'Trainer/epoch': 12, 'Trainer/steps_train': 52}
INFO 2025-09-08 16:39:04,951 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:05,505 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:12,046 train_utils.py: 317: Train Epoch: [13][0/4] | Batch Time: 6.40 (6.40) | Data Time: 5.30 (5.30) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 3.05e+00 (3.05e+00)
INFO 2025-09-08 16:39:14,159 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:39:14,161 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:14,161 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.9899896066635847, 'Losses/train_all_loss_mask': 0.004197552116238512, 'Losses/train_all_loss_dice': 0.4324411302804947, 'Losses/train_all_loss_iou': 0.3488069921731949, 'Losses/train_all_loss_class': 0.12479047247688868, 'Losses/train_all_core_loss': 0.9899896066635847, 'Trainer/where': 0.0, 'Trainer/epoch': 13, 'Trainer/steps_train': 56}
INFO 2025-09-08 16:39:14,173 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:14,739 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:20,199 train_utils.py: 317: Train Epoch: [14][0/4] | Batch Time: 5.37 (5.37) | Data Time: 4.31 (4.31) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 2.52e+00 (2.52e+00)
INFO 2025-09-08 16:39:22,767 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:39:22,767 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:22,767 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.570436768233776, 'Losses/train_all_loss_mask': 0.010448924975207774, 'Losses/train_all_loss_dice': 0.708763487637043, 'Losses/train_all_loss_iou': 0.6199649390764534, 'Losses/train_all_loss_class': 0.032729789685618016, 'Losses/train_all_core_loss': 1.570436768233776, 'Trainer/where': 0.0, 'Trainer/epoch': 14, 'Trainer/steps_train': 60}
INFO 2025-09-08 16:39:22,775 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:23,284 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:28,816 train_utils.py: 317: Train Epoch: [15][0/4] | Batch Time: 5.43 (5.43) | Data Time: 4.66 (4.66) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 3.47e-01 (3.47e-01)
INFO 2025-09-08 16:39:30,808 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:39:30,808 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:30,808 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.36386289447546005, 'Losses/train_all_loss_mask': 0.0006830272322986275, 'Losses/train_all_loss_dice': 0.06254377216100693, 'Losses/train_all_loss_iou': 0.03973724786192179, 'Losses/train_all_loss_class': 0.24792132031871006, 'Losses/train_all_core_loss': 0.36386289447546005, 'Trainer/where': 0.0, 'Trainer/epoch': 15, 'Trainer/steps_train': 64}
INFO 2025-09-08 16:39:30,816 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:31,308 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:36,895 train_utils.py: 317: Train Epoch: [16][0/4] | Batch Time: 5.50 (5.50) | Data Time: 4.88 (4.88) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.15e-01 (4.15e-01)
INFO 2025-09-08 16:39:39,174 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:39:39,174 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:39,174 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5587611347436905, 'Losses/train_all_loss_mask': 0.00198705153161427, 'Losses/train_all_loss_dice': 0.1702851727604866, 'Losses/train_all_loss_iou': 0.04796678712591529, 'Losses/train_all_loss_class': 0.30076815720894956, 'Losses/train_all_core_loss': 0.5587611347436905, 'Trainer/where': 0.0, 'Trainer/epoch': 16, 'Trainer/steps_train': 68}
INFO 2025-09-08 16:39:39,182 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:39,699 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:44,920 train_utils.py: 317: Train Epoch: [17][0/4] | Batch Time: 5.14 (5.14) | Data Time: 4.33 (4.33) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.06e-01 (1.06e-01)
INFO 2025-09-08 16:39:46,974 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:39:46,974 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:46,974 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.11652089282870293, 'Losses/train_all_loss_mask': 0.000721604134014342, 'Losses/train_all_loss_dice': 0.06814485043287277, 'Losses/train_all_loss_iou': 0.03377961553633213, 'Losses/train_all_loss_class': 0.00016434352983196732, 'Losses/train_all_core_loss': 0.11652089282870293, 'Trainer/where': 0.0, 'Trainer/epoch': 17, 'Trainer/steps_train': 72}
INFO 2025-09-08 16:39:46,986 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:47,636 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:55,194 train_utils.py: 317: Train Epoch: [18][0/4] | Batch Time: 7.48 (7.48) | Data Time: 5.99 (5.99) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.99e+00 (1.99e+00)
INFO 2025-09-08 16:39:58,119 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:39:58,119 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:39:58,119 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.0434137433767319, 'Losses/train_all_loss_mask': 0.003915728098945692, 'Losses/train_all_loss_dice': 0.5159055888652802, 'Losses/train_all_loss_iou': 0.44907188042998314, 'Losses/train_all_loss_class': 0.00012169922683824552, 'Losses/train_all_core_loss': 1.0434137433767319, 'Trainer/where': 0.0, 'Trainer/epoch': 18, 'Trainer/steps_train': 76}
INFO 2025-09-08 16:39:58,127 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:39:58,582 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:04,131 train_utils.py: 317: Train Epoch: [19][0/4] | Batch Time: 5.45 (5.45) | Data Time: 4.64 (4.64) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.88e-01 (4.88e-01)
INFO 2025-09-08 16:40:06,315 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:40:06,315 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:06,315 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.362988006323576, 'Losses/train_all_loss_mask': 0.0007105011100065894, 'Losses/train_all_loss_dice': 0.09639483690261841, 'Losses/train_all_loss_iou': 0.04750758223235607, 'Losses/train_all_loss_class': 0.2048755661744508, 'Losses/train_all_core_loss': 0.362988006323576, 'Trainer/where': 0.0, 'Trainer/epoch': 19, 'Trainer/steps_train': 80}
INFO 2025-09-08 16:40:06,323 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:06,794 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:12,069 train_utils.py: 317: Train Epoch: [20][0/4] | Batch Time: 5.20 (5.20) | Data Time: 4.51 (4.51) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.43e-01 (1.43e-01)
INFO 2025-09-08 16:40:14,027 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:14,031 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:14,031 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6876420937478542, 'Losses/train_all_loss_mask': 0.0034036750002996996, 'Losses/train_all_loss_dice': 0.2795034125447273, 'Losses/train_all_loss_iou': 0.1684990655630827, 'Losses/train_all_loss_class': 0.1715661201160401, 'Losses/train_all_core_loss': 0.6876420937478542, 'Trainer/where': 0.0, 'Trainer/epoch': 20, 'Trainer/steps_train': 84}
INFO 2025-09-08 16:40:14,035 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:14,439 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:20,113 train_utils.py: 317: Train Epoch: [21][0/4] | Batch Time: 5.60 (5.60) | Data Time: 4.71 (4.71) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.59e-01 (8.59e-01)
INFO 2025-09-08 16:40:22,122 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:22,122 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:22,122 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4092681333422661, 'Losses/train_all_loss_mask': 0.0010286302785971202, 'Losses/train_all_loss_dice': 0.1603740006685257, 'Losses/train_all_loss_iou': 0.1031824885867536, 'Losses/train_all_loss_class': 0.12513903787112213, 'Losses/train_all_core_loss': 0.4092681333422661, 'Trainer/where': 0.0, 'Trainer/epoch': 21, 'Trainer/steps_train': 88}
INFO 2025-09-08 16:40:22,130 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:22,576 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:29,083 train_utils.py: 317: Train Epoch: [22][0/4] | Batch Time: 6.43 (6.43) | Data Time: 5.48 (5.48) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 5.52e-01 (5.52e-01)
INFO 2025-09-08 16:40:31,471 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:31,471 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:31,475 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.2089112345129251, 'Losses/train_all_loss_mask': 0.0047415760928010454, 'Losses/train_all_loss_dice': 0.5241234973073006, 'Losses/train_all_loss_iou': 0.5102041065692902, 'Losses/train_all_loss_class': 0.07975213844656537, 'Losses/train_all_core_loss': 1.2089112345129251, 'Trainer/where': 0.0, 'Trainer/epoch': 22, 'Trainer/steps_train': 92}
INFO 2025-09-08 16:40:31,479 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:31,938 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:37,475 train_utils.py: 317: Train Epoch: [23][0/4] | Batch Time: 5.47 (5.47) | Data Time: 4.68 (4.68) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.70e-02 (8.70e-02)
INFO 2025-09-08 16:40:39,718 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:39,718 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:39,718 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4201216045767069, 'Losses/train_all_loss_mask': 0.0010934009624532877, 'Losses/train_all_loss_dice': 0.12040682882070541, 'Losses/train_all_loss_iou': 0.08603625558316708, 'Losses/train_all_loss_class': 0.19181050344195683, 'Losses/train_all_core_loss': 0.4201216045767069, 'Trainer/where': 0.0, 'Trainer/epoch': 23, 'Trainer/steps_train': 96}
INFO 2025-09-08 16:40:39,724 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:40,312 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:45,487 train_utils.py: 317: Train Epoch: [24][0/4] | Batch Time: 5.09 (5.09) | Data Time: 4.42 (4.42) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 9.75e-02 (9.75e-02)
INFO 2025-09-08 16:40:47,370 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:47,370 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:47,370 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2433741483837366, 'Losses/train_all_loss_mask': 0.0006973348354222253, 'Losses/train_all_loss_dice': 0.0648597776889801, 'Losses/train_all_loss_iou': 0.03651470132172108, 'Losses/train_all_loss_class': 0.12805295494035818, 'Losses/train_all_core_loss': 0.2433741483837366, 'Trainer/where': 0.0, 'Trainer/epoch': 24, 'Trainer/steps_train': 100}
INFO 2025-09-08 16:40:47,382 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:47,907 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:53,811 train_utils.py: 317: Train Epoch: [25][0/4] | Batch Time: 5.82 (5.82) | Data Time: 4.60 (4.60) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 6.34e-01 (6.34e-01)
INFO 2025-09-08 16:40:56,369 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:40:56,369 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:40:56,369 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.7505182512104511, 'Losses/train_all_loss_mask': 0.020497440025792457, 'Losses/train_all_loss_dice': 0.48191582411527634, 'Losses/train_all_loss_iou': 0.3366291602142155, 'Losses/train_all_loss_class': 0.5220244912652561, 'Losses/train_all_core_loss': 1.7505182512104511, 'Trainer/where': 0.0, 'Trainer/epoch': 25, 'Trainer/steps_train': 104}
INFO 2025-09-08 16:40:56,374 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:40:56,874 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:02,077 train_utils.py: 317: Train Epoch: [26][0/4] | Batch Time: 5.08 (5.08) | Data Time: 4.54 (4.54) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.33e-02 (8.33e-02)
INFO 2025-09-08 16:41:04,582 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:41:04,582 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:41:04,582 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4091073963791132, 'Losses/train_all_loss_mask': 0.0025285575829911977, 'Losses/train_all_loss_dice': 0.23546821624040604, 'Losses/train_all_loss_iou': 0.12293132790364325, 'Losses/train_all_loss_class': 0.00013671878787135938, 'Losses/train_all_core_loss': 0.4091073963791132, 'Trainer/where': 0.0, 'Trainer/epoch': 26, 'Trainer/steps_train': 108}
INFO 2025-09-08 16:41:04,590 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:05,123 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:10,352 train_utils.py: 317: Train Epoch: [27][0/4] | Batch Time: 5.15 (5.15) | Data Time: 4.36 (4.36) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.15e-01 (1.15e-01)
INFO 2025-09-08 16:41:13,427 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:41:13,427 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:41:13,427 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7480982728302479, 'Losses/train_all_loss_mask': 0.0022263795690378174, 'Losses/train_all_loss_dice': 0.2933691218495369, 'Losses/train_all_loss_iou': 0.3213279820047319, 'Losses/train_all_loss_class': 0.08887354966282146, 'Losses/train_all_core_loss': 0.7480982728302479, 'Trainer/where': 0.0, 'Trainer/epoch': 27, 'Trainer/steps_train': 112}
INFO 2025-09-08 16:41:13,431 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:13,898 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:19,343 train_utils.py: 317: Train Epoch: [28][0/4] | Batch Time: 5.36 (5.36) | Data Time: 4.43 (4.43) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 4.87e-01 (4.87e-01)
INFO 2025-09-08 16:41:21,361 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:41:21,361 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:41:21,361 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6588844116777182, 'Losses/train_all_loss_mask': 0.0019978607051598374, 'Losses/train_all_loss_dice': 0.27903539687395096, 'Losses/train_all_loss_iou': 0.3084555082023144, 'Losses/train_all_loss_class': 0.03143632565115695, 'Losses/train_all_core_loss': 0.6588844116777182, 'Trainer/where': 0.0, 'Trainer/epoch': 28, 'Trainer/steps_train': 116}
INFO 2025-09-08 16:41:21,368 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:21,872 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:27,055 train_utils.py: 317: Train Epoch: [29][0/4] | Batch Time: 5.08 (5.08) | Data Time: 4.37 (4.37) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 1.15e+00 (1.15e+00)
INFO 2025-09-08 16:41:29,018 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:41:29,018 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:41:29,018 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5347821805626154, 'Losses/train_all_loss_mask': 0.0013239615364000201, 'Losses/train_all_loss_dice': 0.14856351912021637, 'Losses/train_all_loss_iou': 0.08787987660616636, 'Losses/train_all_loss_class': 0.2718595475744223, 'Losses/train_all_core_loss': 0.5347821805626154, 'Trainer/where': 0.0, 'Trainer/epoch': 29, 'Trainer/steps_train': 120}
INFO 2025-09-08 16:41:29,026 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:41:29,510 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:22,295 train_utils.py: 126: MACHINE SEED: 3690
INFO 2025-09-08 16:51:22,295 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 16:51:22,295 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=23317
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 16:51:22,299 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:51:22,300 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml/tensorboard
INFO 2025-09-08 16:51:22,741 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 16:51:22,741 trainer.py:1243: ====================
INFO 2025-09-08 16:51:22,741 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 16:51:22,741 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 16:51:22,745 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 16:51:22,745 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 16:51:22,745 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 16:51:22,745 trainer.py:1253: ====================
INFO 2025-09-08 16:51:22,750 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:51:22,750 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:51:22,821 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:51:22,838 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.norm1.weight'}
INFO 2025-09-08 16:51:22,840 optimizer.py: 248: Matches for param_name [*bias*]: {'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'memory_attention.layers.3.norm3.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'memory_attention.layers.2.linear2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'memory_attention.layers.1.linear1.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'sam_mask_decoder.conv_s0.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'obj_ptr_tpos_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'memory_attention.layers.1.linear2.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'memory_attention.layers.3.norm2.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.0.norm3.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'obj_ptr_proj.layers.0.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'memory_attention.layers.2.norm2.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'memory_encoder.pix_feat_proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'image_encoder.neck.convs.1.conv.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.3.linear2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm1.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'memory_attention.layers.0.norm2.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'memory_attention.layers.0.linear2.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'memory_attention.layers.0.linear1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'obj_ptr_proj.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'mask_downsample.bias', 'memory_encoder.out_proj.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'obj_ptr_proj.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.3.linear1.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'memory_attention.layers.2.linear1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias'}
INFO 2025-09-08 16:51:22,841 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'image_encoder.trunk.blocks.9.norm2.bias', 'memory_attention.layers.3.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'memory_attention.layers.0.norm3.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'memory_attention.layers.2.norm2.weight', 'memory_attention.layers.1.norm3.weight', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.0.norm3.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'memory_attention.layers.2.norm3.weight', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.11.norm2.weight', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'memory_attention.layers.0.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'memory_attention.layers.3.norm1.weight', 'memory_attention.layers.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'memory_attention.layers.1.norm2.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'memory_attention.layers.0.norm2.weight', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'memory_attention.layers.0.norm1.bias', 'memory_attention.layers.2.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.weight'} 
INFO 2025-09-08 16:51:23,420 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 16:51:23,420 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 16:51:23,424 train_utils.py: 343: Found checkpoint file: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:23,424 trainer.py: 524: Resuming training from C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:23,424 trainer.py: 527: Loading checkpoint file...
INFO 2025-09-08 16:51:23,581 trainer.py: 530: Checkpoint file loaded successfully
INFO 2025-09-08 16:51:23,582 trainer.py: 532: Loading model state dict...
INFO 2025-09-08 16:51:23,641 trainer.py: 538: Model state dict loaded successfully
INFO 2025-09-08 16:51:23,641 trainer.py: 540: Loading optimizer state dict...
INFO 2025-09-08 16:51:23,642 trainer.py: 542: Optimizer state dict loaded successfully
INFO 2025-09-08 16:51:23,643 trainer.py: 544: Loading loss state dict...
INFO 2025-09-08 16:51:23,643 trainer.py: 546: Loss state dict loaded successfully
INFO 2025-09-08 16:51:23,643 trainer.py: 551: Resuming from epoch 30, steps {'train': 120, 'val': 0}
INFO 2025-09-08 16:51:23,643 trainer.py: 554: Loading AMP scaler state dict...
INFO 2025-09-08 16:51:23,643 trainer.py: 556: AMP scaler state dict loaded successfully
INFO 2025-09-08 16:51:23,644 trainer.py: 565: Checkpoint loading completed successfully
INFO 2025-09-08 16:51:23,645 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 16:51:23,645 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 16:51:23,645 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 16:51:23,646 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 16:51:23,646 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 16:51:23,646 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 16:51:23,787 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 16:51:23,787 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 16:51:23,792 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 16:51:23,792 trainer.py: 261: Trainer initialization completed
INFO 2025-09-08 16:51:43,915 train_utils.py: 126: MACHINE SEED: 3690
INFO 2025-09-08 16:51:43,917 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 16:51:43,917 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=28481
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 16:51:43,918 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:51:43,919 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml/tensorboard
INFO 2025-09-08 16:51:44,369 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 16:51:44,373 trainer.py:1243: ====================
INFO 2025-09-08 16:51:44,373 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 16:51:44,375 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 16:51:44,377 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 16:51:44,377 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 16:51:44,377 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 16:51:44,377 trainer.py:1253: ====================
INFO 2025-09-08 16:51:44,381 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 16:51:44,382 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:51:44,456 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 16:51:44,473 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight'}
INFO 2025-09-08 16:51:44,475 optimizer.py: 248: Matches for param_name [*bias*]: {'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'memory_attention.layers.2.norm2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'memory_attention.norm.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'sam_mask_decoder.conv_s0.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'image_encoder.neck.convs.2.conv.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'memory_attention.layers.2.norm3.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'memory_attention.layers.3.linear1.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'obj_ptr_proj.layers.2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.neck.convs.0.conv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'memory_attention.layers.3.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'memory_attention.layers.2.linear1.bias', 'memory_attention.layers.0.linear1.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'obj_ptr_proj.layers.1.bias', 'memory_encoder.out_proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'memory_attention.layers.1.linear2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'memory_attention.layers.0.linear2.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'memory_attention.layers.2.linear2.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'memory_attention.layers.3.linear2.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'obj_ptr_tpos_proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'memory_attention.layers.0.norm3.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'mask_downsample.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'memory_encoder.pix_feat_proj.bias', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.1.linear1.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias'}
INFO 2025-09-08 16:51:44,477 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'memory_attention.layers.2.norm2.bias', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'memory_attention.layers.0.norm3.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'memory_attention.layers.2.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'memory_attention.layers.0.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.3.norm3.weight', 'memory_attention.layers.3.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'memory_attention.layers.0.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'memory_attention.layers.1.norm1.weight', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'memory_attention.layers.2.norm3.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.19.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm3.weight', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'memory_attention.layers.2.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.8.norm2.weight'} 
INFO 2025-09-08 16:51:45,053 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 16:51:45,053 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 16:51:45,056 train_utils.py: 340: Checkpoint file does not exist: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:45,198 trainer.py: 517: Loading pretrained checkpoint from {'_partial_': True, '_target_': 'training.utils.checkpoint_utils.load_state_dict_into_model', 'strict': True, 'ignore_unexpected_keys': None, 'ignore_missing_keys': None, 'state_dict': {'_target_': 'training.utils.checkpoint_utils.load_checkpoint_and_apply_kernels', 'checkpoint_path': 'checkpoints/sam2.1_hiera_base_plus.pt', 'ckpt_state_dict_keys': ['model']}}
INFO 2025-09-08 16:51:45,261 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 16:51:45,261 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 16:51:45,261 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 16:51:45,261 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 16:51:45,261 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 16:51:45,261 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 16:51:45,394 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 16:51:45,398 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 16:51:45,398 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 16:51:45,398 trainer.py: 261: Trainer initialization completed
INFO 2025-09-08 16:51:51,232 train_utils.py: 317: Train Epoch: [0][0/4] | Batch Time: 5.83 (5.83) | Data Time: 4.57 (4.57) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 3.59e-01 (3.59e-01)
INFO 2025-09-08 16:51:53,469 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 16:51:53,469 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:51:53,469 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7371016070246696, 'Losses/train_all_loss_mask': 0.0032584584841970354, 'Losses/train_all_loss_dice': 0.2737828940153122, 'Losses/train_all_loss_iou': 0.30841244757175446, 'Losses/train_all_loss_class': 0.08973711130056472, 'Losses/train_all_core_loss': 0.7371016070246696, 'Trainer/where': 0.0, 'Trainer/epoch': 0, 'Trainer/steps_train': 4}
INFO 2025-09-08 16:51:53,476 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:53,736 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:51:59,081 train_utils.py: 317: Train Epoch: [1][0/4] | Batch Time: 5.25 (5.25) | Data Time: 4.44 (4.44) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.62e-01 (2.62e-01)
INFO 2025-09-08 16:52:01,330 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 16:52:01,330 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:01,330 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6112263593822718, 'Losses/train_all_loss_mask': 0.004403540342536871, 'Losses/train_all_loss_dice': 0.31714580953121185, 'Losses/train_all_loss_iou': 0.15220089675858617, 'Losses/train_all_loss_class': 0.05380886875354918, 'Losses/train_all_core_loss': 0.6112263593822718, 'Trainer/where': 0.0, 'Trainer/epoch': 1, 'Trainer/steps_train': 8}
INFO 2025-09-08 16:52:01,335 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:01,727 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:06,817 train_utils.py: 317: Train Epoch: [2][0/4] | Batch Time: 5.02 (5.02) | Data Time: 4.29 (4.29) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.00e-01 (2.00e-01)
INFO 2025-09-08 16:52:08,846 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:08,846 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:08,847 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6553265564143658, 'Losses/train_all_loss_mask': 0.0034441582902218215, 'Losses/train_all_loss_dice': 0.2847025394439697, 'Losses/train_all_loss_iou': 0.15912650059908628, 'Losses/train_all_loss_class': 0.14261432734929258, 'Losses/train_all_core_loss': 0.6553265564143658, 'Trainer/where': 0.0, 'Trainer/epoch': 2, 'Trainer/steps_train': 12}
INFO 2025-09-08 16:52:08,851 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:09,242 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:14,246 train_utils.py: 317: Train Epoch: [3][0/4] | Batch Time: 4.93 (4.93) | Data Time: 4.14 (4.14) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 8.26e-02 (8.26e-02)
INFO 2025-09-08 16:52:16,238 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:16,238 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:16,238 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5533813647925854, 'Losses/train_all_loss_mask': 0.0006868674427096266, 'Losses/train_all_loss_dice': 0.12461648136377335, 'Losses/train_all_loss_iou': 0.07291077869012952, 'Losses/train_all_loss_class': 0.34211675168626243, 'Losses/train_all_core_loss': 0.5533813647925854, 'Trainer/where': 0.0, 'Trainer/epoch': 3, 'Trainer/steps_train': 16}
INFO 2025-09-08 16:52:16,242 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:16,638 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:21,614 train_utils.py: 317: Train Epoch: [4][0/4] | Batch Time: 4.91 (4.91) | Data Time: 4.10 (4.10) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.26e+00 (1.26e+00)
INFO 2025-09-08 16:52:23,540 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:23,540 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:23,540 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6950331013649702, 'Losses/train_all_loss_mask': 0.0012995629294891842, 'Losses/train_all_loss_dice': 0.2281598597764969, 'Losses/train_all_loss_iou': 0.21880601393058896, 'Losses/train_all_loss_class': 0.22207597654414712, 'Losses/train_all_core_loss': 0.6950331013649702, 'Trainer/where': 0.0, 'Trainer/epoch': 4, 'Trainer/steps_train': 20}
INFO 2025-09-08 16:52:23,542 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:23,937 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:28,786 train_utils.py: 317: Train Epoch: [5][0/4] | Batch Time: 4.78 (4.78) | Data Time: 4.17 (4.17) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.23e-01 (1.23e-01)
INFO 2025-09-08 16:52:30,820 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:30,824 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:30,824 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.21048945374786854, 'Losses/train_all_loss_mask': 0.0008936460071709007, 'Losses/train_all_loss_dice': 0.1202208548784256, 'Losses/train_all_loss_iou': 0.07219724589958787, 'Losses/train_all_loss_class': 0.00019843682093778625, 'Losses/train_all_core_loss': 0.21048945374786854, 'Trainer/where': 0.0, 'Trainer/epoch': 5, 'Trainer/steps_train': 24}
INFO 2025-09-08 16:52:30,828 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:31,249 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:36,632 train_utils.py: 317: Train Epoch: [6][0/4] | Batch Time: 5.30 (5.30) | Data Time: 4.49 (4.49) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.94e+00 (1.94e+00)
INFO 2025-09-08 16:52:39,045 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:39,045 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:39,045 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.1100987941026688, 'Losses/train_all_loss_mask': 0.008136966382153332, 'Losses/train_all_loss_dice': 0.37174442410469055, 'Losses/train_all_loss_iou': 0.19891836494207382, 'Losses/train_all_loss_class': 0.376696680286841, 'Losses/train_all_core_loss': 1.1100987941026688, 'Trainer/where': 0.0, 'Trainer/epoch': 6, 'Trainer/steps_train': 28}
INFO 2025-09-08 16:52:39,053 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:39,440 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:44,910 train_utils.py: 317: Train Epoch: [7][0/4] | Batch Time: 5.40 (5.40) | Data Time: 4.46 (4.46) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.12e+00 (6.12e+00)
INFO 2025-09-08 16:52:46,915 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:46,915 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:46,915 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 2.2520583122968674, 'Losses/train_all_loss_mask': 0.018374109276919626, 'Losses/train_all_loss_dice': 0.9622166380286217, 'Losses/train_all_loss_iou': 0.6167945838533342, 'Losses/train_all_loss_class': 0.3055649737079875, 'Losses/train_all_core_loss': 2.2520583122968674, 'Trainer/where': 0.0, 'Trainer/epoch': 7, 'Trainer/steps_train': 32}
INFO 2025-09-08 16:52:46,919 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:47,353 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:52,936 train_utils.py: 317: Train Epoch: [8][0/4] | Batch Time: 5.49 (5.49) | Data Time: 4.41 (4.41) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.35e+00 (1.35e+00)
INFO 2025-09-08 16:52:55,006 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:52:55,006 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:52:55,006 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4955861270427704, 'Losses/train_all_loss_mask': 0.0028858057848992757, 'Losses/train_all_loss_dice': 0.2832159325480461, 'Losses/train_all_loss_iou': 0.12641933094710112, 'Losses/train_all_loss_class': 0.028234747289388906, 'Losses/train_all_core_loss': 0.4955861270427704, 'Trainer/where': 0.0, 'Trainer/epoch': 8, 'Trainer/steps_train': 36}
INFO 2025-09-08 16:52:55,010 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:52:55,414 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:00,593 train_utils.py: 317: Train Epoch: [9][0/4] | Batch Time: 5.11 (5.11) | Data Time: 4.49 (4.49) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.09e-01 (1.09e-01)
INFO 2025-09-08 16:53:02,922 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:53:02,923 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:02,923 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.3230420593172312, 'Losses/train_all_loss_mask': 0.0009305895218858495, 'Losses/train_all_loss_dice': 0.14769680798053741, 'Losses/train_all_loss_iou': 0.09612469701096416, 'Losses/train_all_loss_class': 0.06060877438630996, 'Losses/train_all_core_loss': 0.3230420593172312, 'Trainer/where': 0.0, 'Trainer/epoch': 9, 'Trainer/steps_train': 40}
INFO 2025-09-08 16:53:02,926 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:03,364 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:09,489 train_utils.py: 317: Train Epoch: [10][0/4] | Batch Time: 6.05 (6.05) | Data Time: 5.36 (5.36) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.36e-01 (6.36e-01)
INFO 2025-09-08 16:53:11,197 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 16:53:11,197 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:11,197 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2431792076677084, 'Losses/train_all_loss_mask': 0.00039915504752467256, 'Losses/train_all_loss_dice': 0.05303352326154709, 'Losses/train_all_loss_iou': 0.02757557574659586, 'Losses/train_all_loss_class': 0.1545870064474002, 'Losses/train_all_core_loss': 0.2431792076677084, 'Trainer/where': 0.0, 'Trainer/epoch': 10, 'Trainer/steps_train': 44}
INFO 2025-09-08 16:53:11,201 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:11,618 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:16,963 train_utils.py: 317: Train Epoch: [11][0/4] | Batch Time: 5.26 (5.26) | Data Time: 4.59 (4.59) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.04e-01 (2.04e-01)
INFO 2025-09-08 16:53:18,959 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:53:18,959 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:18,959 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.22843365743756294, 'Losses/train_all_loss_mask': 0.0007070374813338276, 'Losses/train_all_loss_dice': 0.0874171331524849, 'Losses/train_all_loss_iou': 0.04141010041348636, 'Losses/train_all_loss_class': 0.08546568097153795, 'Losses/train_all_core_loss': 0.22843365743756294, 'Trainer/where': 0.0, 'Trainer/epoch': 11, 'Trainer/steps_train': 48}
INFO 2025-09-08 16:53:18,967 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:19,400 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:24,594 train_utils.py: 317: Train Epoch: [12][0/4] | Batch Time: 5.12 (5.12) | Data Time: 4.50 (4.50) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 7.83e-02 (7.83e-02)
INFO 2025-09-08 16:53:27,154 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:53:27,154 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:27,154 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.8673592787235975, 'Losses/train_all_loss_mask': 0.004250451107509434, 'Losses/train_all_loss_dice': 0.41867728531360626, 'Losses/train_all_loss_iou': 0.266520572360605, 'Losses/train_all_loss_class': 0.09715231717564166, 'Losses/train_all_core_loss': 0.8673592787235975, 'Trainer/where': 0.0, 'Trainer/epoch': 12, 'Trainer/steps_train': 52}
INFO 2025-09-08 16:53:27,158 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:27,588 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:33,391 train_utils.py: 317: Train Epoch: [13][0/4] | Batch Time: 5.73 (5.73) | Data Time: 4.62 (4.62) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 3.05e+00 (3.05e+00)
INFO 2025-09-08 16:53:35,199 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:53:35,199 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:35,199 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.9899896066635847, 'Losses/train_all_loss_mask': 0.004197552116238512, 'Losses/train_all_loss_dice': 0.4324411302804947, 'Losses/train_all_loss_iou': 0.3488069921731949, 'Losses/train_all_loss_class': 0.12479047247688868, 'Losses/train_all_core_loss': 0.9899896066635847, 'Trainer/where': 0.0, 'Trainer/epoch': 13, 'Trainer/steps_train': 56}
INFO 2025-09-08 16:53:35,203 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:35,625 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:41,095 train_utils.py: 317: Train Epoch: [14][0/4] | Batch Time: 5.39 (5.39) | Data Time: 4.43 (4.43) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.52e+00 (2.52e+00)
INFO 2025-09-08 16:53:44,057 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:53:44,057 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:44,057 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.570436768233776, 'Losses/train_all_loss_mask': 0.010448924975207774, 'Losses/train_all_loss_dice': 0.708763487637043, 'Losses/train_all_loss_iou': 0.6199649390764534, 'Losses/train_all_loss_class': 0.032729789685618016, 'Losses/train_all_core_loss': 1.570436768233776, 'Trainer/where': 0.0, 'Trainer/epoch': 14, 'Trainer/steps_train': 60}
INFO 2025-09-08 16:53:44,061 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:44,570 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:51,644 train_utils.py: 317: Train Epoch: [15][0/4] | Batch Time: 6.99 (6.99) | Data Time: 6.17 (6.17) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 3.47e-01 (3.47e-01)
INFO 2025-09-08 16:53:53,748 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:53:53,748 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:53:53,748 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.36386289447546005, 'Losses/train_all_loss_mask': 0.0006830272322986275, 'Losses/train_all_loss_dice': 0.06254377216100693, 'Losses/train_all_loss_iou': 0.03973724786192179, 'Losses/train_all_loss_class': 0.24792132031871006, 'Losses/train_all_core_loss': 0.36386289447546005, 'Trainer/where': 0.0, 'Trainer/epoch': 15, 'Trainer/steps_train': 64}
INFO 2025-09-08 16:53:53,757 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:53:54,281 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:00,055 train_utils.py: 317: Train Epoch: [16][0/4] | Batch Time: 5.70 (5.70) | Data Time: 4.87 (4.87) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.15e-01 (4.15e-01)
INFO 2025-09-08 16:54:02,273 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:54:02,273 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:02,273 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5587611347436905, 'Losses/train_all_loss_mask': 0.00198705153161427, 'Losses/train_all_loss_dice': 0.1702851727604866, 'Losses/train_all_loss_iou': 0.04796678712591529, 'Losses/train_all_loss_class': 0.30076815720894956, 'Losses/train_all_core_loss': 0.5587611347436905, 'Trainer/where': 0.0, 'Trainer/epoch': 16, 'Trainer/steps_train': 68}
INFO 2025-09-08 16:54:02,281 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:02,773 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:07,814 train_utils.py: 317: Train Epoch: [17][0/4] | Batch Time: 4.97 (4.97) | Data Time: 4.28 (4.28) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.06e-01 (1.06e-01)
INFO 2025-09-08 16:54:09,693 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:54:09,693 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:09,693 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.11652089282870293, 'Losses/train_all_loss_mask': 0.000721604134014342, 'Losses/train_all_loss_dice': 0.06814485043287277, 'Losses/train_all_loss_iou': 0.03377961553633213, 'Losses/train_all_loss_class': 0.00016434352983196732, 'Losses/train_all_core_loss': 0.11652089282870293, 'Trainer/where': 0.0, 'Trainer/epoch': 17, 'Trainer/steps_train': 72}
INFO 2025-09-08 16:54:09,697 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:10,214 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:15,655 train_utils.py: 317: Train Epoch: [18][0/4] | Batch Time: 5.37 (5.37) | Data Time: 4.32 (4.32) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.99e+00 (1.99e+00)
INFO 2025-09-08 16:54:18,376 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:54:18,376 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:18,376 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.0434137433767319, 'Losses/train_all_loss_mask': 0.003915728098945692, 'Losses/train_all_loss_dice': 0.5159055888652802, 'Losses/train_all_loss_iou': 0.44907188042998314, 'Losses/train_all_loss_class': 0.00012169922683824552, 'Losses/train_all_core_loss': 1.0434137433767319, 'Trainer/where': 0.0, 'Trainer/epoch': 18, 'Trainer/steps_train': 76}
INFO 2025-09-08 16:54:18,388 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:18,843 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:24,022 train_utils.py: 317: Train Epoch: [19][0/4] | Batch Time: 5.10 (5.10) | Data Time: 4.37 (4.37) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.88e-01 (4.88e-01)
INFO 2025-09-08 16:54:26,300 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 16:54:26,300 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:26,304 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.362988006323576, 'Losses/train_all_loss_mask': 0.0007105011100065894, 'Losses/train_all_loss_dice': 0.09639483690261841, 'Losses/train_all_loss_iou': 0.04750758223235607, 'Losses/train_all_loss_class': 0.2048755661744508, 'Losses/train_all_core_loss': 0.362988006323576, 'Trainer/where': 0.0, 'Trainer/epoch': 19, 'Trainer/steps_train': 80}
INFO 2025-09-08 16:54:26,309 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:26,867 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:31,991 train_utils.py: 317: Train Epoch: [20][0/4] | Batch Time: 5.05 (5.05) | Data Time: 4.36 (4.36) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.43e-01 (1.43e-01)
INFO 2025-09-08 16:54:33,967 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:54:33,967 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:33,967 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6876420937478542, 'Losses/train_all_loss_mask': 0.0034036750002996996, 'Losses/train_all_loss_dice': 0.2795034125447273, 'Losses/train_all_loss_iou': 0.1684990655630827, 'Losses/train_all_loss_class': 0.1715661201160401, 'Losses/train_all_core_loss': 0.6876420937478542, 'Trainer/where': 0.0, 'Trainer/epoch': 20, 'Trainer/steps_train': 84}
INFO 2025-09-08 16:54:33,975 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:34,500 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:40,313 train_utils.py: 317: Train Epoch: [21][0/4] | Batch Time: 5.74 (5.74) | Data Time: 4.66 (4.66) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 8.59e-01 (8.59e-01)
INFO 2025-09-08 16:54:42,245 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:54:42,245 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:42,245 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4092681333422661, 'Losses/train_all_loss_mask': 0.0010286302785971202, 'Losses/train_all_loss_dice': 0.1603740006685257, 'Losses/train_all_loss_iou': 0.1031824885867536, 'Losses/train_all_loss_class': 0.12513903787112213, 'Losses/train_all_core_loss': 0.4092681333422661, 'Trainer/where': 0.0, 'Trainer/epoch': 21, 'Trainer/steps_train': 88}
INFO 2025-09-08 16:54:42,249 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:42,712 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:48,509 train_utils.py: 317: Train Epoch: [22][0/4] | Batch Time: 5.72 (5.72) | Data Time: 4.68 (4.68) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 5.52e-01 (5.52e-01)
INFO 2025-09-08 16:54:51,161 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:54:51,165 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:51,165 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.2089112345129251, 'Losses/train_all_loss_mask': 0.0047415760928010454, 'Losses/train_all_loss_dice': 0.5241234973073006, 'Losses/train_all_loss_iou': 0.5102041065692902, 'Losses/train_all_loss_class': 0.07975213844656537, 'Losses/train_all_core_loss': 1.2089112345129251, 'Trainer/where': 0.0, 'Trainer/epoch': 22, 'Trainer/steps_train': 92}
INFO 2025-09-08 16:54:51,171 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:51,603 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:56,569 train_utils.py: 317: Train Epoch: [23][0/4] | Batch Time: 4.90 (4.90) | Data Time: 4.31 (4.31) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.70e-02 (8.70e-02)
INFO 2025-09-08 16:54:58,728 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:54:58,728 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:54:58,728 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4201216045767069, 'Losses/train_all_loss_mask': 0.0010934009624532877, 'Losses/train_all_loss_dice': 0.12040682882070541, 'Losses/train_all_loss_iou': 0.08603625558316708, 'Losses/train_all_loss_class': 0.19181050344195683, 'Losses/train_all_core_loss': 0.4201216045767069, 'Trainer/where': 0.0, 'Trainer/epoch': 23, 'Trainer/steps_train': 96}
INFO 2025-09-08 16:54:58,736 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:54:59,223 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:04,981 train_utils.py: 317: Train Epoch: [24][0/4] | Batch Time: 5.68 (5.68) | Data Time: 4.91 (4.91) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 9.75e-02 (9.75e-02)
INFO 2025-09-08 16:55:06,661 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:06,661 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:06,661 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2433741483837366, 'Losses/train_all_loss_mask': 0.0006973348354222253, 'Losses/train_all_loss_dice': 0.0648597776889801, 'Losses/train_all_loss_iou': 0.03651470132172108, 'Losses/train_all_loss_class': 0.12805295494035818, 'Losses/train_all_core_loss': 0.2433741483837366, 'Trainer/where': 0.0, 'Trainer/epoch': 24, 'Trainer/steps_train': 100}
INFO 2025-09-08 16:55:06,669 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:07,090 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:12,672 train_utils.py: 317: Train Epoch: [25][0/4] | Batch Time: 5.51 (5.51) | Data Time: 4.58 (4.58) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 6.34e-01 (6.34e-01)
INFO 2025-09-08 16:55:14,843 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:14,843 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:14,843 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.7505182512104511, 'Losses/train_all_loss_mask': 0.020497440025792457, 'Losses/train_all_loss_dice': 0.48191582411527634, 'Losses/train_all_loss_iou': 0.3366291602142155, 'Losses/train_all_loss_class': 0.5220244912652561, 'Losses/train_all_core_loss': 1.7505182512104511, 'Trainer/where': 0.0, 'Trainer/epoch': 25, 'Trainer/steps_train': 104}
INFO 2025-09-08 16:55:14,852 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:15,260 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:20,531 train_utils.py: 317: Train Epoch: [26][0/4] | Batch Time: 5.20 (5.20) | Data Time: 4.50 (4.50) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.33e-02 (8.33e-02)
INFO 2025-09-08 16:55:22,839 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:22,839 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:22,839 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4091073963791132, 'Losses/train_all_loss_mask': 0.0025285575829911977, 'Losses/train_all_loss_dice': 0.23546821624040604, 'Losses/train_all_loss_iou': 0.12293132790364325, 'Losses/train_all_loss_class': 0.00013671878787135938, 'Losses/train_all_core_loss': 0.4091073963791132, 'Trainer/where': 0.0, 'Trainer/epoch': 26, 'Trainer/steps_train': 108}
INFO 2025-09-08 16:55:22,847 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:23,373 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:29,364 train_utils.py: 317: Train Epoch: [27][0/4] | Batch Time: 5.92 (5.92) | Data Time: 5.21 (5.21) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.15e-01 (1.15e-01)
INFO 2025-09-08 16:55:31,951 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:31,951 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:31,951 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7480982728302479, 'Losses/train_all_loss_mask': 0.0022263795690378174, 'Losses/train_all_loss_dice': 0.2933691218495369, 'Losses/train_all_loss_iou': 0.3213279820047319, 'Losses/train_all_loss_class': 0.08887354966282146, 'Losses/train_all_core_loss': 0.7480982728302479, 'Trainer/where': 0.0, 'Trainer/epoch': 27, 'Trainer/steps_train': 112}
INFO 2025-09-08 16:55:31,955 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:32,384 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:38,038 train_utils.py: 317: Train Epoch: [28][0/4] | Batch Time: 5.58 (5.58) | Data Time: 4.55 (4.55) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 4.87e-01 (4.87e-01)
INFO 2025-09-08 16:55:40,014 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:40,014 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:40,014 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6588844116777182, 'Losses/train_all_loss_mask': 0.0019978607051598374, 'Losses/train_all_loss_dice': 0.27903539687395096, 'Losses/train_all_loss_iou': 0.3084555082023144, 'Losses/train_all_loss_class': 0.03143632565115695, 'Losses/train_all_core_loss': 0.6588844116777182, 'Trainer/where': 0.0, 'Trainer/epoch': 28, 'Trainer/steps_train': 116}
INFO 2025-09-08 16:55:40,017 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:40,459 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:45,754 train_utils.py: 317: Train Epoch: [29][0/4] | Batch Time: 5.22 (5.22) | Data Time: 4.54 (4.54) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 1.15e+00 (1.15e+00)
INFO 2025-09-08 16:55:47,643 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 16:55:47,643 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 16:55:47,643 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5347821805626154, 'Losses/train_all_loss_mask': 0.0013239615364000201, 'Losses/train_all_loss_dice': 0.14856351912021637, 'Losses/train_all_loss_iou': 0.08787987660616636, 'Losses/train_all_loss_class': 0.2718595475744223, 'Losses/train_all_core_loss': 0.5347821805626154, 'Trainer/where': 0.0, 'Trainer/epoch': 29, 'Trainer/steps_train': 120}
INFO 2025-09-08 16:55:47,650 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 16:55:48,117 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:06,346 train_utils.py: 126: MACHINE SEED: 3690
INFO 2025-09-08 17:56:06,351 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 17:56:06,351 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=43614
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 17:56:06,352 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 17:56:06,352 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml/tensorboard
INFO 2025-09-08 17:56:06,831 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 17:56:06,831 trainer.py:1243: ====================
INFO 2025-09-08 17:56:06,831 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 17:56:06,835 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 17:56:06,835 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 17:56:06,837 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 17:56:06,838 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 17:56:06,838 trainer.py:1253: ====================
INFO 2025-09-08 17:56:06,840 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 17:56:06,841 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 17:56:06,909 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 17:56:06,923 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias'}
INFO 2025-09-08 17:56:06,926 optimizer.py: 248: Matches for param_name [*bias*]: {'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'mask_downsample.bias', 'memory_attention.layers.2.norm2.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'memory_attention.layers.1.linear1.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'memory_attention.layers.1.linear2.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'obj_ptr_tpos_proj.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'memory_attention.layers.0.linear1.bias', 'memory_attention.layers.3.linear2.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'memory_attention.layers.0.linear2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'obj_ptr_proj.layers.1.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'sam_mask_decoder.conv_s0.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'memory_attention.layers.0.norm2.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'memory_attention.layers.2.linear1.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'memory_encoder.out_proj.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'obj_ptr_proj.layers.2.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'memory_encoder.pix_feat_proj.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'sam_mask_decoder.conv_s1.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'memory_attention.layers.3.linear1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'memory_attention.layers.3.norm1.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.neck.convs.2.conv.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'memory_attention.layers.3.norm3.bias', 'memory_attention.layers.2.linear2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.neck.convs.3.conv.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias'}
INFO 2025-09-08 17:56:06,927 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.1.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'memory_attention.layers.0.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.7.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'memory_attention.layers.1.norm1.bias', 'memory_attention.layers.0.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'memory_attention.layers.0.norm3.weight', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.weight', 'memory_attention.layers.0.norm1.bias', 'memory_attention.layers.2.norm3.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.1.norm3.weight', 'memory_attention.layers.2.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.12.norm2.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'memory_attention.layers.3.norm1.bias', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'memory_attention.layers.1.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'memory_attention.layers.3.norm3.bias', 'memory_attention.layers.2.norm2.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'memory_attention.norm.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.3.norm2.bias'} 
INFO 2025-09-08 17:56:07,489 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 17:56:07,490 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 17:56:07,490 train_utils.py: 343: Found checkpoint file: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:07,490 trainer.py: 524: Resuming training from C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:07,491 trainer.py: 527: Loading checkpoint file...
INFO 2025-09-08 17:56:07,656 trainer.py: 530: Checkpoint file loaded successfully
INFO 2025-09-08 17:56:07,656 trainer.py: 532: Loading model state dict...
INFO 2025-09-08 17:56:07,713 trainer.py: 538: Model state dict loaded successfully
INFO 2025-09-08 17:56:07,713 trainer.py: 540: Loading optimizer state dict...
INFO 2025-09-08 17:56:07,717 trainer.py: 542: Optimizer state dict loaded successfully
INFO 2025-09-08 17:56:07,717 trainer.py: 544: Loading loss state dict...
INFO 2025-09-08 17:56:07,717 trainer.py: 546: Loss state dict loaded successfully
INFO 2025-09-08 17:56:07,717 trainer.py: 551: Resuming from epoch 30, steps {'train': 120, 'val': 0}
INFO 2025-09-08 17:56:07,717 trainer.py: 554: Loading AMP scaler state dict...
INFO 2025-09-08 17:56:07,717 trainer.py: 556: AMP scaler state dict loaded successfully
INFO 2025-09-08 17:56:07,717 trainer.py: 565: Checkpoint loading completed successfully
INFO 2025-09-08 17:56:07,717 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 17:56:07,717 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 17:56:07,717 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 17:56:07,717 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 17:56:07,717 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 17:56:07,717 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 17:56:07,922 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 17:56:07,922 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 17:56:07,922 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 17:56:07,922 trainer.py: 261: Trainer initialization completed
INFO 2025-09-08 17:56:28,640 train_utils.py: 126: MACHINE SEED: 3690
INFO 2025-09-08 17:56:28,645 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 17:56:28,645 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=41769
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 17:56:28,645 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 17:56:28,645 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_CUSTOM_8GB.yaml/tensorboard
INFO 2025-09-08 17:56:29,058 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 17:56:29,058 trainer.py:1243: ====================
INFO 2025-09-08 17:56:29,058 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 17:56:29,061 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 17:56:29,063 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 17:56:29,063 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 17:56:29,063 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 17:56:29,064 trainer.py:1253: ====================
INFO 2025-09-08 17:56:29,068 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 17:56:29,068 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 17:56:29,150 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 17:56:29,164 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias'}
INFO 2025-09-08 17:56:29,165 optimizer.py: 248: Matches for param_name [*bias*]: {'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'memory_attention.layers.2.norm1.bias', 'memory_attention.layers.3.norm2.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'memory_attention.layers.3.linear2.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'memory_attention.layers.3.linear1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'memory_attention.layers.2.linear1.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'obj_ptr_proj.layers.2.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'memory_attention.layers.3.norm3.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'obj_ptr_proj.layers.1.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'memory_attention.layers.0.linear2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'memory_attention.layers.0.norm3.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'memory_attention.layers.1.norm1.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'obj_ptr_tpos_proj.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.2.norm2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'image_encoder.neck.convs.2.conv.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'memory_attention.layers.1.linear2.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'memory_attention.layers.0.linear1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'memory_attention.layers.1.norm2.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'mask_downsample.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'sam_mask_decoder.conv_s0.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'memory_attention.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'memory_attention.layers.2.linear2.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'memory_attention.layers.1.linear1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.neck.convs.1.conv.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'memory_encoder.out_proj.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'memory_encoder.pix_feat_proj.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'image_encoder.neck.convs.3.conv.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias'}
INFO 2025-09-08 17:56:29,167 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'memory_attention.layers.0.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'memory_attention.layers.1.norm3.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'memory_attention.layers.2.norm1.bias', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.22.norm2.weight', 'memory_attention.layers.2.norm2.weight', 'memory_attention.layers.0.norm3.weight', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'memory_attention.norm.weight', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'memory_attention.layers.0.norm3.bias', 'memory_attention.layers.1.norm1.bias', 'memory_attention.norm.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'memory_attention.layers.2.norm2.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'memory_attention.layers.2.norm1.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'memory_attention.layers.2.norm3.weight', 'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'memory_attention.layers.0.norm2.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.22.norm1.bias'} 
INFO 2025-09-08 17:56:29,761 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 17:56:29,761 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 17:56:29,761 train_utils.py: 340: Checkpoint file does not exist: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:29,890 trainer.py: 517: Loading pretrained checkpoint from {'_partial_': True, '_target_': 'training.utils.checkpoint_utils.load_state_dict_into_model', 'strict': True, 'ignore_unexpected_keys': None, 'ignore_missing_keys': None, 'state_dict': {'_target_': 'training.utils.checkpoint_utils.load_checkpoint_and_apply_kernels', 'checkpoint_path': 'checkpoints/sam2.1_hiera_base_plus.pt', 'ckpt_state_dict_keys': ['model']}}
INFO 2025-09-08 17:56:29,953 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 17:56:29,953 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 17:56:29,953 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 17:56:29,953 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 17:56:29,953 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 17:56:29,953 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 17:56:30,090 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 17:56:30,090 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 17:56:30,095 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 17:56:30,095 trainer.py: 261: Trainer initialization completed
INFO 2025-09-08 17:56:36,211 train_utils.py: 317: Train Epoch: [0][0/4] | Batch Time: 6.12 (6.12) | Data Time: 4.90 (4.90) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 3.59e-01 (3.59e-01)
INFO 2025-09-08 17:56:38,636 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 17:56:38,636 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:56:38,636 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7371016070246696, 'Losses/train_all_loss_mask': 0.0032584584841970354, 'Losses/train_all_loss_dice': 0.2737828940153122, 'Losses/train_all_loss_iou': 0.30841244757175446, 'Losses/train_all_loss_class': 0.08973711130056472, 'Losses/train_all_core_loss': 0.7371016070246696, 'Trainer/where': 0.0, 'Trainer/epoch': 0, 'Trainer/steps_train': 4}
INFO 2025-09-08 17:56:38,641 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:38,919 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:44,164 train_utils.py: 317: Train Epoch: [1][0/4] | Batch Time: 5.16 (5.16) | Data Time: 4.50 (4.50) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.62e-01 (2.62e-01)
INFO 2025-09-08 17:56:46,377 trainer.py:1134: Estimated time remaining: 00d 00h 03m
INFO 2025-09-08 17:56:46,377 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:56:46,377 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6112263593822718, 'Losses/train_all_loss_mask': 0.004403540342536871, 'Losses/train_all_loss_dice': 0.31714580953121185, 'Losses/train_all_loss_iou': 0.15220089675858617, 'Losses/train_all_loss_class': 0.05380886875354918, 'Losses/train_all_core_loss': 0.6112263593822718, 'Trainer/where': 0.0, 'Trainer/epoch': 1, 'Trainer/steps_train': 8}
INFO 2025-09-08 17:56:46,382 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:46,791 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:52,022 train_utils.py: 317: Train Epoch: [2][0/4] | Batch Time: 5.16 (5.16) | Data Time: 4.36 (4.36) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.00e-01 (2.00e-01)
INFO 2025-09-08 17:56:54,145 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:56:54,147 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:56:54,147 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6553265564143658, 'Losses/train_all_loss_mask': 0.0034441582902218215, 'Losses/train_all_loss_dice': 0.2847025394439697, 'Losses/train_all_loss_iou': 0.15912650059908628, 'Losses/train_all_loss_class': 0.14261432734929258, 'Losses/train_all_core_loss': 0.6553265564143658, 'Trainer/where': 0.0, 'Trainer/epoch': 2, 'Trainer/steps_train': 12}
INFO 2025-09-08 17:56:54,151 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:54,555 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:56:59,413 train_utils.py: 317: Train Epoch: [3][0/4] | Batch Time: 4.79 (4.79) | Data Time: 4.17 (4.17) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 8.26e-02 (8.26e-02)
INFO 2025-09-08 17:57:01,385 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:01,388 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:01,388 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5533813647925854, 'Losses/train_all_loss_mask': 0.0006868674427096266, 'Losses/train_all_loss_dice': 0.12461648136377335, 'Losses/train_all_loss_iou': 0.07291077869012952, 'Losses/train_all_loss_class': 0.34211675168626243, 'Losses/train_all_core_loss': 0.5533813647925854, 'Trainer/where': 0.0, 'Trainer/epoch': 3, 'Trainer/steps_train': 16}
INFO 2025-09-08 17:57:01,393 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:01,814 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:06,875 train_utils.py: 317: Train Epoch: [4][0/4] | Batch Time: 4.99 (4.99) | Data Time: 4.14 (4.14) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.26e+00 (1.26e+00)
INFO 2025-09-08 17:57:08,851 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:08,851 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:08,851 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6950331013649702, 'Losses/train_all_loss_mask': 0.0012995629294891842, 'Losses/train_all_loss_dice': 0.2281598597764969, 'Losses/train_all_loss_iou': 0.21880601393058896, 'Losses/train_all_loss_class': 0.22207597654414712, 'Losses/train_all_core_loss': 0.6950331013649702, 'Trainer/where': 0.0, 'Trainer/epoch': 4, 'Trainer/steps_train': 20}
INFO 2025-09-08 17:57:08,860 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:09,258 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:14,108 train_utils.py: 317: Train Epoch: [5][0/4] | Batch Time: 4.78 (4.78) | Data Time: 4.10 (4.10) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.23e-01 (1.23e-01)
INFO 2025-09-08 17:57:16,179 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:16,179 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:16,179 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.21048945374786854, 'Losses/train_all_loss_mask': 0.0008936460071709007, 'Losses/train_all_loss_dice': 0.1202208548784256, 'Losses/train_all_loss_iou': 0.07219724589958787, 'Losses/train_all_loss_class': 0.00019843682093778625, 'Losses/train_all_core_loss': 0.21048945374786854, 'Trainer/where': 0.0, 'Trainer/epoch': 5, 'Trainer/steps_train': 24}
INFO 2025-09-08 17:57:16,184 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:16,605 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:22,041 train_utils.py: 317: Train Epoch: [6][0/4] | Batch Time: 5.37 (5.37) | Data Time: 4.34 (4.34) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.94e+00 (1.94e+00)
INFO 2025-09-08 17:57:24,466 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:24,466 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:24,466 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.1100987941026688, 'Losses/train_all_loss_mask': 0.008136966382153332, 'Losses/train_all_loss_dice': 0.37174442410469055, 'Losses/train_all_loss_iou': 0.19891836494207382, 'Losses/train_all_loss_class': 0.376696680286841, 'Losses/train_all_core_loss': 1.1100987941026688, 'Trainer/where': 0.0, 'Trainer/epoch': 6, 'Trainer/steps_train': 28}
INFO 2025-09-08 17:57:24,470 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:24,883 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:30,441 train_utils.py: 317: Train Epoch: [7][0/4] | Batch Time: 5.49 (5.49) | Data Time: 4.46 (4.46) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.12e+00 (6.12e+00)
INFO 2025-09-08 17:57:32,328 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:32,328 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:32,328 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 2.2520583122968674, 'Losses/train_all_loss_mask': 0.018374109276919626, 'Losses/train_all_loss_dice': 0.9622166380286217, 'Losses/train_all_loss_iou': 0.6167945838533342, 'Losses/train_all_loss_class': 0.3055649737079875, 'Losses/train_all_core_loss': 2.2520583122968674, 'Trainer/where': 0.0, 'Trainer/epoch': 7, 'Trainer/steps_train': 32}
INFO 2025-09-08 17:57:32,333 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:32,753 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:38,313 train_utils.py: 317: Train Epoch: [8][0/4] | Batch Time: 5.49 (5.49) | Data Time: 4.41 (4.41) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.35e+00 (1.35e+00)
INFO 2025-09-08 17:57:40,319 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:40,324 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:40,324 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4955861270427704, 'Losses/train_all_loss_mask': 0.0028858057848992757, 'Losses/train_all_loss_dice': 0.2832159325480461, 'Losses/train_all_loss_iou': 0.12641933094710112, 'Losses/train_all_loss_class': 0.028234747289388906, 'Losses/train_all_core_loss': 0.4955861270427704, 'Trainer/where': 0.0, 'Trainer/epoch': 8, 'Trainer/steps_train': 36}
INFO 2025-09-08 17:57:40,332 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:40,757 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:45,940 train_utils.py: 317: Train Epoch: [9][0/4] | Batch Time: 5.08 (5.08) | Data Time: 4.41 (4.41) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.09e-01 (1.09e-01)
INFO 2025-09-08 17:57:48,277 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:57:48,277 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:48,282 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.3230420593172312, 'Losses/train_all_loss_mask': 0.0009305895218858495, 'Losses/train_all_loss_dice': 0.14769680798053741, 'Losses/train_all_loss_iou': 0.09612469701096416, 'Losses/train_all_loss_class': 0.06060877438630996, 'Losses/train_all_core_loss': 0.3230420593172312, 'Trainer/where': 0.0, 'Trainer/epoch': 9, 'Trainer/steps_train': 40}
INFO 2025-09-08 17:57:48,290 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:48,740 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:53,914 train_utils.py: 317: Train Epoch: [10][0/4] | Batch Time: 5.10 (5.10) | Data Time: 4.50 (4.50) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 6.36e-01 (6.36e-01)
INFO 2025-09-08 17:57:55,652 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:57:55,652 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:57:55,652 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2431792076677084, 'Losses/train_all_loss_mask': 0.00039915504752467256, 'Losses/train_all_loss_dice': 0.05303352326154709, 'Losses/train_all_loss_iou': 0.02757557574659586, 'Losses/train_all_loss_class': 0.1545870064474002, 'Losses/train_all_core_loss': 0.2431792076677084, 'Trainer/where': 0.0, 'Trainer/epoch': 10, 'Trainer/steps_train': 44}
INFO 2025-09-08 17:57:55,660 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:57:56,060 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:01,552 train_utils.py: 317: Train Epoch: [11][0/4] | Batch Time: 5.42 (5.42) | Data Time: 4.70 (4.70) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.04e-01 (2.04e-01)
INFO 2025-09-08 17:58:03,591 trainer.py:1134: Estimated time remaining: 00d 00h 02m
INFO 2025-09-08 17:58:03,591 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:03,593 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.22843365743756294, 'Losses/train_all_loss_mask': 0.0007070374813338276, 'Losses/train_all_loss_dice': 0.0874171331524849, 'Losses/train_all_loss_iou': 0.04141010041348636, 'Losses/train_all_loss_class': 0.08546568097153795, 'Losses/train_all_core_loss': 0.22843365743756294, 'Trainer/where': 0.0, 'Trainer/epoch': 11, 'Trainer/steps_train': 48}
INFO 2025-09-08 17:58:03,597 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:04,009 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:09,122 train_utils.py: 317: Train Epoch: [12][0/4] | Batch Time: 5.04 (5.04) | Data Time: 4.39 (4.39) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 7.83e-02 (7.83e-02)
INFO 2025-09-08 17:58:11,789 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:11,789 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:11,789 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.8673592787235975, 'Losses/train_all_loss_mask': 0.004250451107509434, 'Losses/train_all_loss_dice': 0.41867728531360626, 'Losses/train_all_loss_iou': 0.266520572360605, 'Losses/train_all_loss_class': 0.09715231717564166, 'Losses/train_all_core_loss': 0.8673592787235975, 'Trainer/where': 0.0, 'Trainer/epoch': 12, 'Trainer/steps_train': 52}
INFO 2025-09-08 17:58:11,797 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:12,234 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:18,016 train_utils.py: 317: Train Epoch: [13][0/4] | Batch Time: 5.70 (5.70) | Data Time: 4.73 (4.73) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 3.05e+00 (3.05e+00)
INFO 2025-09-08 17:58:19,772 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:19,775 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:19,775 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.9899896066635847, 'Losses/train_all_loss_mask': 0.004197552116238512, 'Losses/train_all_loss_dice': 0.4324411302804947, 'Losses/train_all_loss_iou': 0.3488069921731949, 'Losses/train_all_loss_class': 0.12479047247688868, 'Losses/train_all_core_loss': 0.9899896066635847, 'Trainer/where': 0.0, 'Trainer/epoch': 13, 'Trainer/steps_train': 56}
INFO 2025-09-08 17:58:19,780 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:20,184 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:26,087 train_utils.py: 317: Train Epoch: [14][0/4] | Batch Time: 5.83 (5.83) | Data Time: 4.87 (4.87) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.52e+00 (2.52e+00)
INFO 2025-09-08 17:58:28,436 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:28,437 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:28,437 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.570436768233776, 'Losses/train_all_loss_mask': 0.010448924975207774, 'Losses/train_all_loss_dice': 0.708763487637043, 'Losses/train_all_loss_iou': 0.6199649390764534, 'Losses/train_all_loss_class': 0.032729789685618016, 'Losses/train_all_core_loss': 1.570436768233776, 'Trainer/where': 0.0, 'Trainer/epoch': 14, 'Trainer/steps_train': 60}
INFO 2025-09-08 17:58:28,441 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:28,850 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:34,162 train_utils.py: 317: Train Epoch: [15][0/4] | Batch Time: 5.24 (5.24) | Data Time: 4.55 (4.55) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 3.47e-01 (3.47e-01)
INFO 2025-09-08 17:58:35,903 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:35,904 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:35,904 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.36386289447546005, 'Losses/train_all_loss_mask': 0.0006830272322986275, 'Losses/train_all_loss_dice': 0.06254377216100693, 'Losses/train_all_loss_iou': 0.03973724786192179, 'Losses/train_all_loss_class': 0.24792132031871006, 'Losses/train_all_core_loss': 0.36386289447546005, 'Trainer/where': 0.0, 'Trainer/epoch': 15, 'Trainer/steps_train': 64}
INFO 2025-09-08 17:58:35,908 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:36,320 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:41,941 train_utils.py: 317: Train Epoch: [16][0/4] | Batch Time: 5.55 (5.55) | Data Time: 4.94 (4.94) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.15e-01 (4.15e-01)
INFO 2025-09-08 17:58:43,941 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:43,941 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:43,941 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5587611347436905, 'Losses/train_all_loss_mask': 0.00198705153161427, 'Losses/train_all_loss_dice': 0.1702851727604866, 'Losses/train_all_loss_iou': 0.04796678712591529, 'Losses/train_all_loss_class': 0.30076815720894956, 'Losses/train_all_core_loss': 0.5587611347436905, 'Trainer/where': 0.0, 'Trainer/epoch': 16, 'Trainer/steps_train': 68}
INFO 2025-09-08 17:58:43,945 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:44,366 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:50,515 train_utils.py: 317: Train Epoch: [17][0/4] | Batch Time: 6.08 (6.08) | Data Time: 5.41 (5.41) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.06e-01 (1.06e-01)
INFO 2025-09-08 17:58:52,269 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:58:52,269 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:58:52,269 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.11652089282870293, 'Losses/train_all_loss_mask': 0.000721604134014342, 'Losses/train_all_loss_dice': 0.06814485043287277, 'Losses/train_all_loss_iou': 0.03377961553633213, 'Losses/train_all_loss_class': 0.00016434352983196732, 'Losses/train_all_core_loss': 0.11652089282870293, 'Trainer/where': 0.0, 'Trainer/epoch': 17, 'Trainer/steps_train': 72}
INFO 2025-09-08 17:58:52,274 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:52,719 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:58:58,016 train_utils.py: 317: Train Epoch: [18][0/4] | Batch Time: 5.22 (5.22) | Data Time: 4.32 (4.32) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.99e+00 (1.99e+00)
INFO 2025-09-08 17:59:00,544 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:59:00,548 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:00,548 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.0434137433767319, 'Losses/train_all_loss_mask': 0.003915728098945692, 'Losses/train_all_loss_dice': 0.5159055888652802, 'Losses/train_all_loss_iou': 0.44907188042998314, 'Losses/train_all_loss_class': 0.00012169922683824552, 'Losses/train_all_core_loss': 1.0434137433767319, 'Trainer/where': 0.0, 'Trainer/epoch': 18, 'Trainer/steps_train': 76}
INFO 2025-09-08 17:59:00,552 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:00,960 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:06,118 train_utils.py: 317: Train Epoch: [19][0/4] | Batch Time: 5.09 (5.09) | Data Time: 4.47 (4.47) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.88e-01 (4.88e-01)
INFO 2025-09-08 17:59:08,606 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:59:08,606 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:08,606 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.362988006323576, 'Losses/train_all_loss_mask': 0.0007105011100065894, 'Losses/train_all_loss_dice': 0.09639483690261841, 'Losses/train_all_loss_iou': 0.04750758223235607, 'Losses/train_all_loss_class': 0.2048755661744508, 'Losses/train_all_core_loss': 0.362988006323576, 'Trainer/where': 0.0, 'Trainer/epoch': 19, 'Trainer/steps_train': 80}
INFO 2025-09-08 17:59:08,614 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:09,161 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:14,818 train_utils.py: 317: Train Epoch: [20][0/4] | Batch Time: 5.56 (5.56) | Data Time: 4.73 (4.73) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.43e-01 (1.43e-01)
INFO 2025-09-08 17:59:16,936 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:59:16,937 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:16,938 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6876420937478542, 'Losses/train_all_loss_mask': 0.0034036750002996996, 'Losses/train_all_loss_dice': 0.2795034125447273, 'Losses/train_all_loss_iou': 0.1684990655630827, 'Losses/train_all_loss_class': 0.1715661201160401, 'Losses/train_all_core_loss': 0.6876420937478542, 'Trainer/where': 0.0, 'Trainer/epoch': 20, 'Trainer/steps_train': 84}
INFO 2025-09-08 17:59:16,948 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:17,585 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:24,211 train_utils.py: 317: Train Epoch: [21][0/4] | Batch Time: 6.41 (6.41) | Data Time: 5.26 (5.26) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 8.59e-01 (8.59e-01)
INFO 2025-09-08 17:59:26,350 trainer.py:1134: Estimated time remaining: 00d 00h 01m
INFO 2025-09-08 17:59:26,350 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:26,350 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4092681333422661, 'Losses/train_all_loss_mask': 0.0010286302785971202, 'Losses/train_all_loss_dice': 0.1603740006685257, 'Losses/train_all_loss_iou': 0.1031824885867536, 'Losses/train_all_loss_class': 0.12513903787112213, 'Losses/train_all_core_loss': 0.4092681333422661, 'Trainer/where': 0.0, 'Trainer/epoch': 21, 'Trainer/steps_train': 88}
INFO 2025-09-08 17:59:26,359 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:26,880 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:32,512 train_utils.py: 317: Train Epoch: [22][0/4] | Batch Time: 5.54 (5.54) | Data Time: 4.56 (4.56) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 5.52e-01 (5.52e-01)
INFO 2025-09-08 17:59:35,133 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 17:59:35,133 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:35,133 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.2089112345129251, 'Losses/train_all_loss_mask': 0.0047415760928010454, 'Losses/train_all_loss_dice': 0.5241234973073006, 'Losses/train_all_loss_iou': 0.5102041065692902, 'Losses/train_all_loss_class': 0.07975213844656537, 'Losses/train_all_core_loss': 1.2089112345129251, 'Trainer/where': 0.0, 'Trainer/epoch': 22, 'Trainer/steps_train': 92}
INFO 2025-09-08 17:59:35,146 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:35,679 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:41,708 train_utils.py: 317: Train Epoch: [23][0/4] | Batch Time: 5.95 (5.95) | Data Time: 5.18 (5.18) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.70e-02 (8.70e-02)
INFO 2025-09-08 17:59:44,175 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 17:59:44,175 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:44,175 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4201216045767069, 'Losses/train_all_loss_mask': 0.0010934009624532877, 'Losses/train_all_loss_dice': 0.12040682882070541, 'Losses/train_all_loss_iou': 0.08603625558316708, 'Losses/train_all_loss_class': 0.19181050344195683, 'Losses/train_all_core_loss': 0.4201216045767069, 'Trainer/where': 0.0, 'Trainer/epoch': 23, 'Trainer/steps_train': 96}
INFO 2025-09-08 17:59:44,183 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:44,729 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:49,904 train_utils.py: 317: Train Epoch: [24][0/4] | Batch Time: 5.09 (5.09) | Data Time: 4.47 (4.47) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 9.75e-02 (9.75e-02)
INFO 2025-09-08 17:59:51,649 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 17:59:51,649 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 17:59:51,649 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.2433741483837366, 'Losses/train_all_loss_mask': 0.0006973348354222253, 'Losses/train_all_loss_dice': 0.0648597776889801, 'Losses/train_all_loss_iou': 0.03651470132172108, 'Losses/train_all_loss_class': 0.12805295494035818, 'Losses/train_all_core_loss': 0.2433741483837366, 'Trainer/where': 0.0, 'Trainer/epoch': 24, 'Trainer/steps_train': 100}
INFO 2025-09-08 17:59:51,653 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:52,058 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 17:59:57,774 train_utils.py: 317: Train Epoch: [25][0/4] | Batch Time: 5.65 (5.65) | Data Time: 4.78 (4.78) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 6.34e-01 (6.34e-01)
INFO 2025-09-08 18:00:00,094 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 18:00:00,094 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 18:00:00,094 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 1.7505182512104511, 'Losses/train_all_loss_mask': 0.020497440025792457, 'Losses/train_all_loss_dice': 0.48191582411527634, 'Losses/train_all_loss_iou': 0.3366291602142155, 'Losses/train_all_loss_class': 0.5220244912652561, 'Losses/train_all_core_loss': 1.7505182512104511, 'Trainer/where': 0.0, 'Trainer/epoch': 25, 'Trainer/steps_train': 104}
INFO 2025-09-08 18:00:00,099 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:00,572 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:06,602 train_utils.py: 317: Train Epoch: [26][0/4] | Batch Time: 5.95 (5.95) | Data Time: 5.26 (5.26) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.33e-02 (8.33e-02)
INFO 2025-09-08 18:00:08,969 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 18:00:08,969 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 18:00:08,969 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.4091073963791132, 'Losses/train_all_loss_mask': 0.0025285575829911977, 'Losses/train_all_loss_dice': 0.23546821624040604, 'Losses/train_all_loss_iou': 0.12293132790364325, 'Losses/train_all_loss_class': 0.00013671878787135938, 'Losses/train_all_core_loss': 0.4091073963791132, 'Trainer/where': 0.0, 'Trainer/epoch': 26, 'Trainer/steps_train': 108}
INFO 2025-09-08 18:00:08,973 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:09,382 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:14,752 train_utils.py: 317: Train Epoch: [27][0/4] | Batch Time: 5.28 (5.28) | Data Time: 4.63 (4.63) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.15e-01 (1.15e-01)
INFO 2025-09-08 18:00:17,252 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 18:00:17,252 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 18:00:17,252 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.7480982728302479, 'Losses/train_all_loss_mask': 0.0022263795690378174, 'Losses/train_all_loss_dice': 0.2933691218495369, 'Losses/train_all_loss_iou': 0.3213279820047319, 'Losses/train_all_loss_class': 0.08887354966282146, 'Losses/train_all_core_loss': 0.7480982728302479, 'Trainer/where': 0.0, 'Trainer/epoch': 27, 'Trainer/steps_train': 112}
INFO 2025-09-08 18:00:17,260 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:17,702 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:23,147 train_utils.py: 317: Train Epoch: [28][0/4] | Batch Time: 5.37 (5.37) | Data Time: 4.50 (4.50) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 4.87e-01 (4.87e-01)
INFO 2025-09-08 18:00:25,002 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 18:00:25,002 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 18:00:25,005 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.6588844116777182, 'Losses/train_all_loss_mask': 0.0019978607051598374, 'Losses/train_all_loss_dice': 0.27903539687395096, 'Losses/train_all_loss_iou': 0.3084555082023144, 'Losses/train_all_loss_class': 0.03143632565115695, 'Losses/train_all_core_loss': 0.6588844116777182, 'Trainer/where': 0.0, 'Trainer/epoch': 28, 'Trainer/steps_train': 116}
INFO 2025-09-08 18:00:25,010 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:25,464 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:30,668 train_utils.py: 317: Train Epoch: [29][0/4] | Batch Time: 5.13 (5.13) | Data Time: 4.44 (4.44) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 1.15e+00 (1.15e+00)
INFO 2025-09-08 18:00:32,530 trainer.py:1134: Estimated time remaining: 00d 00h 00m
INFO 2025-09-08 18:00:32,530 trainer.py:1076: Synchronizing meters
INFO 2025-09-08 18:00:32,530 trainer.py: 981: Losses and meters: {'Losses/train_all_loss': 0.5347821805626154, 'Losses/train_all_loss_mask': 0.0013239615364000201, 'Losses/train_all_loss_dice': 0.14856351912021637, 'Losses/train_all_loss_iou': 0.08787987660616636, 'Losses/train_all_loss_class': 0.2718595475744223, 'Losses/train_all_core_loss': 0.5347821805626154, 'Trainer/where': 0.0, 'Trainer/epoch': 29, 'Trainer/steps_train': 120}
INFO 2025-09-08 18:00:32,534 trainer.py: 440: Saving checkpoint to: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 18:00:32,951 trainer.py: 460: Checkpoint saved successfully: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_CUSTOM_8GB.yaml\checkpoints\checkpoint.pt
