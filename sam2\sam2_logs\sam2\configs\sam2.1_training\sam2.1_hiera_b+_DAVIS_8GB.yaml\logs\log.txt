INFO 2025-07-03 09:40:55,078 train_utils.py: 108: MACHINE SEED: 4920
INFO 2025-07-03 09:40:55,123 train_utils.py: 154: Logging ENV_VARIABLES
INFO 2025-07-03 09:40:55,123 train_utils.py: 155: ACLOCAL_PATH=C:\Program Files\Git\mingw64\share\aclocal;C:\Program Files\Git\usr\share\aclocal
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_15716_YYDHYLPDEXMOIBFH
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_ALLOW_SOFTLINKS=false
CONDA_DEFAULT_ENV=base
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(base) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_ROOT=C:\ProgramDatanaconda3
CONDA_SHLVL=1
CONFIG_SITE=C:/Program Files/Git/etc/config.site
CUDA_MODULE_LOADING=LAZY
DISPLAY=needs-to-be-defined
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_15540_1262719628=1
EFC_15540_1592913036=1
EFC_15540_2283032206=1
EFC_15540_2775293581=1
EFC_15540_3789132940=1
EXEPATH=C:\Program Files\Git\bin
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
GIT_PAGER=cat
HOME=C:\Users\<USER>\Users\user
HOSTNAME=MSI
HYDRA_FULL_ERROR=1
INFOPATH=C:\Program Files\Git\mingw64\local\info;C:\Program Files\Git\mingw64\share\info;C:\Program Files\Git\usr\local\info;C:\Program Files\Git\usr\share\info;C:\Program Files\Git\usr\info;C:\Program Files\Git\share\info
LANG=en_US.UTF-8
LESS=-FX
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MANPATH=C:\Program Files\Git\mingw64\local\man;C:\Program Files\Git\mingw64\share\man;C:\Program Files\Git\usr\local\man;C:\Program Files\Git\usr\share\man;C:\Program Files\Git\usr\man;C:\Program Files\Git\share\man
MASTER_ADDR=localhost
MASTER_PORT=51489
MINGW_CHOST=x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX=mingw-w64-x86_64
MINGW_PREFIX=C:/Program Files/Git/mingw64
MSYSTEM=MINGW64
MSYSTEM_CARCH=x86_64
MSYSTEM_CHOST=x86_64-w64-mingw32
MSYSTEM_PREFIX=C:/Program Files/Git/mingw64
NUMBER_OF_PROCESSORS=32
OLDPWD=C:/Users/<USER>/Codings/sam2davis/sam2
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_PATH=C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\ProgramData\anaconda3;C:\ProgramData\anaconda3\Library\mingw-w64\bin;C:\ProgramData\anaconda3\Library\usr\bin;C:\ProgramData\anaconda3\Library\bin;C:\ProgramData\anaconda3\Scripts;C:\ProgramData\anaconda3\bin;C:\ProgramData\anaconda3\condabin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
ORIGINAL_TEMP=C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP=C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PAGER=cat
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\ProgramData\anaconda3;C:\ProgramData\anaconda3\Library\mingw-w64\bin;C:\ProgramData\anaconda3\Library\usr\bin;C:\ProgramData\anaconda3\Library\bin;C:\ProgramData\anaconda3\Scripts;C:\ProgramData\anaconda3\bin;C:\ProgramData\anaconda3\condabin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PKG_CONFIG_PATH=C:\Program Files\Git\mingw64\lib\pkgconfig;C:\Program Files\Git\mingw64\share\pkgconfig
PKG_CONFIG_SYSTEM_INCLUDE_PATH=C:/Program Files/Git/mingw64/include
PKG_CONFIG_SYSTEM_LIBRARY_PATH=C:/Program Files/Git/mingw64/lib
PLINK_PROTOCOL=ssh
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PROMPT=(base) $P$G
PS1=\[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SHELL=C:\Program Files\Git\usr\bin\bash.exe
SHLVL=1
SSH_ASKPASS=C:/Program Files/Git/mingw64/bin/git-askpass.exe
SSL_CERT_FILE=C:\ProgramData\anaconda3\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.101.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_=C:/Users/<USER>/.conda/envs/sam2_env_py310/python.exe
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP=437
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET="1"
__PSLOCKDOWNPOLICY=0

INFO 2025-07-03 09:40:55,123 trainer.py:1049: Setting up components: Model, loss, optim, meters etc.
INFO 2025-07-03 09:40:55,127 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_DAVIS_8GB.yaml/tensorboard
INFO 2025-07-03 09:40:56,009 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-07-03 09:40:56,010 trainer.py:1119: ====================
INFO 2025-07-03 09:40:56,010 trainer.py:1120: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-07-03 09:40:56,014 trainer.py:1121: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-07-03 09:40:56,014 trainer.py:1122: 	Total parameters 80.9 M
INFO 2025-07-03 09:40:56,014 trainer.py:1123: 	Trainable parameters 80.9 M
INFO 2025-07-03 09:40:56,014 trainer.py:1126: 	Non-Trainable parameters 0  
INFO 2025-07-03 09:40:56,014 trainer.py:1129: ====================
INFO 2025-07-03 09:40:56,021 trainer.py:1083: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-07-03 09:40:56,021 trainer.py: 316: Moving components to device cuda:0 and local rank 0.
INFO 2025-07-03 09:40:56,114 trainer.py: 322: Done moving components to device cuda:0 and local rank 0.
INFO 2025-07-03 09:40:56,131 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.norm2.weight'}
INFO 2025-07-03 09:40:56,132 optimizer.py: 248: Matches for param_name [*bias*]: {'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'memory_attention.layers.3.norm2.bias', 'memory_attention.layers.1.linear2.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'memory_encoder.pix_feat_proj.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'obj_ptr_proj.layers.1.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'memory_attention.layers.2.linear1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'memory_attention.layers.0.linear2.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'memory_attention.layers.3.linear2.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.neck.convs.3.conv.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'memory_encoder.out_proj.bias', 'obj_ptr_proj.layers.2.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'obj_ptr_proj.layers.0.bias', 'obj_ptr_tpos_proj.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'memory_attention.layers.0.norm3.bias', 'memory_attention.layers.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'sam_mask_decoder.conv_s0.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'memory_attention.layers.2.linear2.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'memory_attention.layers.1.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'memory_attention.norm.bias', 'mask_downsample.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'memory_attention.layers.1.linear1.bias', 'memory_attention.layers.3.linear1.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'memory_attention.layers.0.linear1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias'}
INFO 2025-07-03 09:40:56,134 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'memory_attention.layers.3.norm2.bias', 'memory_attention.layers.2.norm3.weight', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'memory_attention.layers.3.norm2.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'memory_attention.layers.0.norm3.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.2.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'memory_attention.layers.0.norm2.weight', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'memory_attention.layers.0.norm3.bias', 'memory_attention.layers.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'memory_attention.layers.0.norm1.weight', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'memory_attention.layers.1.norm3.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'memory_attention.layers.2.norm2.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.9.norm2.weight'} 
INFO 2025-07-03 09:40:57,643 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-07-03 09:40:57,968 trainer.py: 419: Loading pretrained checkpoint from {'_partial_': True, '_target_': 'training.utils.checkpoint_utils.load_state_dict_into_model', 'strict': True, 'ignore_unexpected_keys': None, 'ignore_missing_keys': None, 'state_dict': {'_target_': 'training.utils.checkpoint_utils.load_checkpoint_and_apply_kernels', 'checkpoint_path': '../checkpoints/sam2.1_hiera_base_plus.pt', 'ckpt_state_dict_keys': ['model']}}
INFO 2025-07-03 09:41:04,748 train_utils.py: 271: Train Epoch: [0][  0/120] | Batch Time: 6.46 (6.46) | Data Time: 4.24 (4.24) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.91e-01 (2.91e-01)
INFO 2025-07-03 09:41:09,530 train_utils.py: 271: Train Epoch: [0][ 10/120] | Batch Time: 0.49 (1.02) | Data Time: 0.00 (0.39) | Mem (GB): 4.00 (4.73/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 3.46e+00 (1.52e+00)
INFO 2025-07-03 09:41:13,883 train_utils.py: 271: Train Epoch: [0][ 20/120] | Batch Time: 0.37 (0.74) | Data Time: 0.00 (0.20) | Mem (GB): 5.00 (4.81/5.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 4.75e-01 (2.28e+00)
INFO 2025-07-03 09:41:18,929 train_utils.py: 271: Train Epoch: [0][ 30/120] | Batch Time: 0.34 (0.67) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (4.81/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 3.40e-01 (2.49e+00)
INFO 2025-07-03 09:41:23,745 train_utils.py: 271: Train Epoch: [0][ 40/120] | Batch Time: 0.37 (0.62) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (4.95/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.65e-01 (2.50e+00)
INFO 2025-07-03 09:41:28,057 train_utils.py: 271: Train Epoch: [0][ 50/120] | Batch Time: 0.60 (0.58) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.00/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 6.89e+00 (2.47e+00)
INFO 2025-07-03 09:41:33,036 train_utils.py: 271: Train Epoch: [0][ 60/120] | Batch Time: 0.36 (0.57) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.05/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 1.61e-01 (2.53e+00)
INFO 2025-07-03 09:41:36,813 train_utils.py: 271: Train Epoch: [0][ 70/120] | Batch Time: 0.33 (0.54) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.04/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 8.95e-01 (2.29e+00)
INFO 2025-07-03 09:41:41,541 train_utils.py: 271: Train Epoch: [0][ 80/120] | Batch Time: 0.35 (0.53) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.09/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.95e-01 (2.37e+00)
INFO 2025-07-03 09:41:46,119 train_utils.py: 271: Train Epoch: [0][ 90/120] | Batch Time: 0.33 (0.53) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.10/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 2.00e-01 (2.33e+00)
INFO 2025-07-03 09:41:50,435 train_utils.py: 271: Train Epoch: [0][100/120] | Batch Time: 0.65 (0.52) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.11/6.00) | Time Elapsed: 00d 00h 00m | Losses/train_all_loss: 6.68e+00 (2.30e+00)
INFO 2025-07-03 09:41:55,423 train_utils.py: 271: Train Epoch: [0][110/120] | Batch Time: 0.57 (0.51) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.13/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.73e+00 (2.27e+00)
INFO 2025-07-03 09:41:59,829 trainer.py:1010: Estimated time remaining: 00d 00h 39m
INFO 2025-07-03 09:41:59,829 trainer.py: 952: Synchronizing meters
INFO 2025-07-03 09:41:59,829 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.1695605256905157, 'Losses/train_all_loss_mask': 0.033613552399522934, 'Losses/train_all_loss_dice': 1.0298447641233603, 'Losses/train_all_loss_iou': 0.45774653999639364, 'Losses/train_all_loss_class': 0.009698170430237952, 'Losses/train_all_core_loss': 2.1695605256905157, 'Trainer/where': 0.019791666666666666, 'Trainer/epoch': 0, 'Trainer/steps_train': 120}
INFO 2025-07-03 09:42:05,080 train_utils.py: 271: Train Epoch: [1][  0/120] | Batch Time: 4.32 (4.32) | Data Time: 3.52 (3.52) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 5.73e+00 (5.73e+00)
INFO 2025-07-03 09:42:09,784 train_utils.py: 271: Train Epoch: [1][ 10/120] | Batch Time: 0.33 (0.82) | Data Time: 0.00 (0.32) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.15e-01 (3.98e+00)
INFO 2025-07-03 09:42:14,780 train_utils.py: 271: Train Epoch: [1][ 20/120] | Batch Time: 0.50 (0.67) | Data Time: 0.00 (0.17) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 4.90e+00 (3.83e+00)
INFO 2025-07-03 09:42:19,000 train_utils.py: 271: Train Epoch: [1][ 30/120] | Batch Time: 0.30 (0.59) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.27e-01 (3.41e+00)
INFO 2025-07-03 09:42:24,079 train_utils.py: 271: Train Epoch: [1][ 40/120] | Batch Time: 0.35 (0.57) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.66e-01 (3.37e+00)
INFO 2025-07-03 09:42:28,321 train_utils.py: 271: Train Epoch: [1][ 50/120] | Batch Time: 0.32 (0.54) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.18e-01 (2.99e+00)
INFO 2025-07-03 09:42:32,720 train_utils.py: 271: Train Epoch: [1][ 60/120] | Batch Time: 0.33 (0.52) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 5.76e-01 (2.66e+00)
INFO 2025-07-03 09:42:37,857 train_utils.py: 271: Train Epoch: [1][ 70/120] | Batch Time: 0.61 (0.52) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.47e+00 (2.54e+00)
INFO 2025-07-03 09:42:43,128 train_utils.py: 271: Train Epoch: [1][ 80/120] | Batch Time: 0.52 (0.52) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.37/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.60e+00 (2.56e+00)
INFO 2025-07-03 09:42:47,733 train_utils.py: 271: Train Epoch: [1][ 90/120] | Batch Time: 0.33 (0.52) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 1.19e-01 (2.50e+00)
INFO 2025-07-03 09:42:52,354 train_utils.py: 271: Train Epoch: [1][100/120] | Batch Time: 0.39 (0.51) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 01m | Losses/train_all_loss: 2.28e-01 (2.52e+00)
INFO 2025-07-03 09:42:57,129 train_utils.py: 271: Train Epoch: [1][110/120] | Batch Time: 0.36 (0.51) | Data Time: 0.00 (0.03) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.02e+00 (2.47e+00)
INFO 2025-07-03 09:43:01,901 trainer.py:1010: Estimated time remaining: 00d 00h 38m
INFO 2025-07-03 09:43:01,901 trainer.py: 952: Synchronizing meters
INFO 2025-07-03 09:43:01,901 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.434610490128398, 'Losses/train_all_loss_mask': 0.03901464353693882, 'Losses/train_all_loss_dice': 1.1943183114131293, 'Losses/train_all_loss_iou': 0.4496511333816064, 'Losses/train_all_loss_class': 0.010348141109686064, 'Losses/train_all_core_loss': 2.434610490128398, 'Trainer/where': 0.04479166666666666, 'Trainer/epoch': 1, 'Trainer/steps_train': 240}
INFO 2025-07-03 09:43:07,114 train_utils.py: 271: Train Epoch: [2][  0/120] | Batch Time: 4.30 (4.30) | Data Time: 3.43 (3.43) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.38e+00 (1.38e+00)
INFO 2025-07-03 09:43:12,273 train_utils.py: 271: Train Epoch: [2][ 10/120] | Batch Time: 0.60 (0.86) | Data Time: 0.00 (0.31) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.02e+01 (3.49e+00)
INFO 2025-07-03 09:43:16,693 train_utils.py: 271: Train Epoch: [2][ 20/120] | Batch Time: 0.36 (0.66) | Data Time: 0.00 (0.16) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.68e-01 (2.95e+00)
INFO 2025-07-03 09:43:20,692 train_utils.py: 271: Train Epoch: [2][ 30/120] | Batch Time: 0.47 (0.58) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.16/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.39e+00 (2.52e+00)
INFO 2025-07-03 09:43:25,565 train_utils.py: 271: Train Epoch: [2][ 40/120] | Batch Time: 0.37 (0.55) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.52e-01 (2.46e+00)
INFO 2025-07-03 09:43:31,037 train_utils.py: 271: Train Epoch: [2][ 50/120] | Batch Time: 0.61 (0.55) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.66e+00 (2.74e+00)
INFO 2025-07-03 09:43:35,888 train_utils.py: 271: Train Epoch: [2][ 60/120] | Batch Time: 0.51 (0.54) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.47e+00 (2.69e+00)
INFO 2025-07-03 09:43:40,672 train_utils.py: 271: Train Epoch: [2][ 70/120] | Batch Time: 0.35 (0.53) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 1.48e+00 (2.64e+00)
INFO 2025-07-03 09:43:45,278 train_utils.py: 271: Train Epoch: [2][ 80/120] | Batch Time: 0.43 (0.52) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 3.84e-01 (2.54e+00)
INFO 2025-07-03 09:43:49,632 train_utils.py: 271: Train Epoch: [2][ 90/120] | Batch Time: 0.36 (0.51) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 02m | Losses/train_all_loss: 4.71e-01 (2.33e+00)
INFO 2025-07-03 09:43:55,011 train_utils.py: 271: Train Epoch: [2][100/120] | Batch Time: 0.39 (0.52) | Data Time: 0.00 (0.03) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.46e-01 (2.30e+00)
INFO 2025-07-03 09:44:00,493 train_utils.py: 271: Train Epoch: [2][110/120] | Batch Time: 0.40 (0.52) | Data Time: 0.00 (0.03) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 3.58e-01 (2.32e+00)
INFO 2025-07-03 09:44:05,740 trainer.py:1010: Estimated time remaining: 00d 00h 38m
INFO 2025-07-03 09:44:05,740 trainer.py: 952: Synchronizing meters
INFO 2025-07-03 09:44:05,740 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.3132498054454724, 'Losses/train_all_loss_mask': 0.034740950203074425, 'Losses/train_all_loss_dice': 1.148859873910745, 'Losses/train_all_loss_iou': 0.44165884867155303, 'Losses/train_all_loss_class': 0.027912100517460202, 'Losses/train_all_core_loss': 2.3132498054454724, 'Trainer/where': 0.06979166666666667, 'Trainer/epoch': 2, 'Trainer/steps_train': 360}
INFO 2025-07-03 09:44:11,518 train_utils.py: 271: Train Epoch: [3][  0/120] | Batch Time: 4.77 (4.77) | Data Time: 4.08 (4.08) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 4.37e+00 (4.37e+00)
INFO 2025-07-03 09:44:16,287 train_utils.py: 271: Train Epoch: [3][ 10/120] | Batch Time: 0.60 (0.87) | Data Time: 0.00 (0.37) | Mem (GB): 6.00 (5.36/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 8.03e+00 (3.14e+00)
INFO 2025-07-03 09:44:20,735 train_utils.py: 271: Train Epoch: [3][ 20/120] | Batch Time: 0.75 (0.67) | Data Time: 0.00 (0.19) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 7.03e+00 (2.56e+00)
INFO 2025-07-03 09:44:25,651 train_utils.py: 271: Train Epoch: [3][ 30/120] | Batch Time: 0.35 (0.61) | Data Time: 0.00 (0.13) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.02e+00 (2.50e+00)
INFO 2025-07-03 09:44:31,454 train_utils.py: 271: Train Epoch: [3][ 40/120] | Batch Time: 0.62 (0.60) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 2.27e+00 (2.51e+00)
INFO 2025-07-03 09:44:36,622 train_utils.py: 271: Train Epoch: [3][ 50/120] | Batch Time: 0.55 (0.59) | Data Time: 0.01 (0.08) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.37e+00 (2.34e+00)
INFO 2025-07-03 09:44:41,296 train_utils.py: 271: Train Epoch: [3][ 60/120] | Batch Time: 0.38 (0.57) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.24e-01 (2.40e+00)
INFO 2025-07-03 09:44:46,025 train_utils.py: 271: Train Epoch: [3][ 70/120] | Batch Time: 0.70 (0.55) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.30e+01 (2.38e+00)
INFO 2025-07-03 09:44:52,351 train_utils.py: 271: Train Epoch: [3][ 80/120] | Batch Time: 0.72 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 03m | Losses/train_all_loss: 1.82e+00 (2.64e+00)
INFO 2025-07-03 09:44:58,134 train_utils.py: 271: Train Epoch: [3][ 90/120] | Batch Time: 0.68 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 2.14e+00 (2.67e+00)
INFO 2025-07-03 09:45:02,661 train_utils.py: 271: Train Epoch: [3][100/120] | Batch Time: 0.37 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 8.68e-02 (2.48e+00)
INFO 2025-07-03 09:45:08,178 train_utils.py: 271: Train Epoch: [3][110/120] | Batch Time: 0.67 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.30/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 8.19e+00 (2.66e+00)
INFO 2025-07-03 09:45:12,819 trainer.py:1010: Estimated time remaining: 00d 00h 39m
INFO 2025-07-03 09:45:12,819 trainer.py: 952: Synchronizing meters
INFO 2025-07-03 09:45:12,819 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.56030550605307, 'Losses/train_all_loss_mask': 0.03673959992981205, 'Losses/train_all_loss_dice': 1.3291040050486724, 'Losses/train_all_loss_iou': 0.4689768728528482, 'Losses/train_all_loss_class': 0.02743261128295368, 'Losses/train_all_core_loss': 2.56030550605307, 'Trainer/where': 0.09479166666666666, 'Trainer/epoch': 3, 'Trainer/steps_train': 480}
INFO 2025-07-03 09:45:17,906 train_utils.py: 271: Train Epoch: [4][  0/120] | Batch Time: 4.13 (4.13) | Data Time: 3.57 (3.57) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 5.37e-01 (5.37e-01)
INFO 2025-07-03 09:45:23,289 train_utils.py: 271: Train Epoch: [4][ 10/120] | Batch Time: 0.61 (0.86) | Data Time: 0.00 (0.32) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 6.40e+00 (3.19e+00)
INFO 2025-07-03 09:45:27,731 train_utils.py: 271: Train Epoch: [4][ 20/120] | Batch Time: 0.74 (0.66) | Data Time: 0.00 (0.17) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 2.25e+00 (2.38e+00)
INFO 2025-07-03 09:45:33,022 train_utils.py: 271: Train Epoch: [4][ 30/120] | Batch Time: 0.62 (0.62) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 9.64e-01 (2.63e+00)
INFO 2025-07-03 09:45:37,047 train_utils.py: 271: Train Epoch: [4][ 40/120] | Batch Time: 0.39 (0.57) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 2.69e-01 (2.17e+00)
INFO 2025-07-03 09:45:42,278 train_utils.py: 271: Train Epoch: [4][ 50/120] | Batch Time: 0.64 (0.56) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 3.04e+00 (2.41e+00)
INFO 2025-07-03 09:45:48,025 train_utils.py: 271: Train Epoch: [4][ 60/120] | Batch Time: 0.67 (0.56) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 5.46e+00 (2.59e+00)
INFO 2025-07-03 09:45:52,820 train_utils.py: 271: Train Epoch: [4][ 70/120] | Batch Time: 0.34 (0.55) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 3.32e-01 (2.47e+00)
INFO 2025-07-03 09:45:58,050 train_utils.py: 271: Train Epoch: [4][ 80/120] | Batch Time: 0.74 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.26/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 1.29e+00 (2.39e+00)
INFO 2025-07-03 09:46:04,101 train_utils.py: 271: Train Epoch: [4][ 90/120] | Batch Time: 0.68 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 5.06e+00 (2.43e+00)
INFO 2025-07-03 09:46:09,805 train_utils.py: 271: Train Epoch: [4][100/120] | Batch Time: 0.69 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 3.04e+00 (2.65e+00)
INFO 2025-07-05 18:19:41,180 train_utils.py: 108: MACHINE SEED: 4920
INFO 2025-07-05 18:19:41,290 train_utils.py: 154: Logging ENV_VARIABLES
INFO 2025-07-05 18:19:41,290 train_utils.py: 155: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_23176_XHPXZDRABCETEFSM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_ALLOW_SOFTLINKS=false
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_ROOT=C:\ProgramData\anaconda3
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_24604_1262719628=1
EFC_24604_1592913036=1
EFC_24604_2283032206=1
EFC_24604_2775293581=1
EFC_24604_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=15497
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PROMPT=(base) $P$G
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\ProgramData\anaconda3\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.101.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP=437
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET="1"
__PSLOCKDOWNPOLICY=0

INFO 2025-07-05 18:19:41,290 trainer.py:1049: Setting up components: Model, loss, optim, meters etc.
INFO 2025-07-05 18:19:41,290 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_DAVIS_8GB.yaml/tensorboard
INFO 2025-07-05 18:19:42,555 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-07-05 18:19:42,560 trainer.py:1119: ====================
INFO 2025-07-05 18:19:42,560 trainer.py:1120: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-07-05 18:19:42,562 trainer.py:1121: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-07-05 18:19:42,564 trainer.py:1122: 	Total parameters 80.9 M
INFO 2025-07-05 18:19:42,564 trainer.py:1123: 	Trainable parameters 80.9 M
INFO 2025-07-05 18:19:42,564 trainer.py:1126: 	Non-Trainable parameters 0  
INFO 2025-07-05 18:19:42,564 trainer.py:1129: ====================
INFO 2025-07-05 18:19:42,569 trainer.py:1083: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-07-05 18:19:42,570 trainer.py: 316: Moving components to device cuda:0 and local rank 0.
INFO 2025-07-05 18:19:42,668 trainer.py: 322: Done moving components to device cuda:0 and local rank 0.
INFO 2025-07-05 18:19:42,686 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.23.norm2.weight'}
INFO 2025-07-05 18:19:42,688 optimizer.py: 248: Matches for param_name [*bias*]: {'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'memory_attention.layers.2.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.0.norm3.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'memory_attention.layers.3.linear1.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'memory_attention.layers.1.linear2.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'memory_attention.layers.0.linear2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.neck.convs.3.conv.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.neck.convs.1.conv.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'memory_attention.layers.3.norm1.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'sam_mask_decoder.conv_s0.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'image_encoder.neck.convs.2.conv.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'mask_downsample.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'memory_attention.layers.2.norm2.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'obj_ptr_tpos_proj.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'memory_attention.layers.3.linear2.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'memory_attention.layers.0.norm2.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'memory_attention.norm.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'sam_mask_decoder.conv_s1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'memory_attention.layers.2.linear2.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'memory_attention.layers.1.linear1.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'obj_ptr_proj.layers.1.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'memory_encoder.out_proj.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'memory_attention.layers.0.linear1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'memory_attention.layers.2.linear1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'memory_encoder.pix_feat_proj.bias', 'obj_ptr_proj.layers.2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias'}
INFO 2025-07-05 18:19:42,689 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'sam_mask_decoder.transformer.layers.1.norm3.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.blocks.12.norm1.weight', 'memory_attention.layers.2.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.18.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'memory_attention.layers.0.norm2.weight', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'memory_attention.layers.3.norm1.weight', 'memory_attention.layers.3.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'memory_attention.layers.3.norm2.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.blocks.17.norm1.weight', 'memory_attention.layers.2.norm2.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'memory_attention.layers.0.norm2.bias', 'memory_attention.layers.2.norm3.bias', 'memory_attention.layers.2.norm1.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'memory_attention.norm.bias', 'memory_attention.layers.1.norm3.weight', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.0.norm3.weight', 'memory_attention.norm.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.2.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.5.norm2.weight', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'memory_attention.layers.0.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'memory_attention.layers.2.norm3.weight', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.23.norm2.weight'} 
INFO 2025-07-05 18:19:45,259 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-07-05 18:19:45,263 trainer.py: 425: Resuming training from C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_DAVIS_8GB.yaml/checkpoints\checkpoint.pt
INFO 2025-07-05 18:19:52,251 train_utils.py: 271: Train Epoch: [4][  0/120] | Batch Time: 6.11 (6.11) | Data Time: 3.54 (3.54) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 8.82e-02 (8.82e-02)
INFO 2025-07-05 18:19:57,021 train_utils.py: 271: Train Epoch: [4][ 10/120] | Batch Time: 0.57 (0.99) | Data Time: 0.00 (0.32) | Mem (GB): 6.00 (5.18/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 5.55e+00 (1.99e+00)
INFO 2025-07-05 18:20:01,202 train_utils.py: 271: Train Epoch: [4][ 20/120] | Batch Time: 0.39 (0.72) | Data Time: 0.00 (0.17) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 3.15e-01 (1.98e+00)
INFO 2025-07-05 18:20:06,324 train_utils.py: 271: Train Epoch: [4][ 30/120] | Batch Time: 0.33 (0.65) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 1.26e-01 (2.01e+00)
INFO 2025-07-05 18:20:11,340 train_utils.py: 271: Train Epoch: [4][ 40/120] | Batch Time: 0.38 (0.61) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 1.51e-01 (1.95e+00)
INFO 2025-07-05 18:20:15,905 train_utils.py: 271: Train Epoch: [4][ 50/120] | Batch Time: 0.61 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 8.98e+00 (1.96e+00)
INFO 2025-07-05 18:20:21,065 train_utils.py: 271: Train Epoch: [4][ 60/120] | Batch Time: 0.41 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.21/6.00) | Time Elapsed: 00d 00h 04m | Losses/train_all_loss: 7.29e-01 (1.92e+00)
INFO 2025-07-05 18:20:25,109 train_utils.py: 271: Train Epoch: [4][ 70/120] | Batch Time: 0.35 (0.55) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 7.21e-01 (1.76e+00)
INFO 2025-07-05 18:20:30,061 train_utils.py: 271: Train Epoch: [4][ 80/120] | Batch Time: 0.41 (0.54) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.21/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 1.65e-01 (1.84e+00)
INFO 2025-07-05 18:20:34,912 train_utils.py: 271: Train Epoch: [4][ 90/120] | Batch Time: 0.33 (0.54) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 3.92e-01 (1.90e+00)
INFO 2025-07-05 18:20:39,564 train_utils.py: 271: Train Epoch: [4][100/120] | Batch Time: 0.76 (0.53) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 2.94e+00 (1.95e+00)
INFO 2025-07-05 18:20:44,973 train_utils.py: 271: Train Epoch: [4][110/120] | Batch Time: 0.65 (0.53) | Data Time: 0.00 (0.03) | Mem (GB): 6.00 (5.23/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 8.88e+00 (2.02e+00)
INFO 2025-07-05 18:20:49,547 trainer.py:1010: Estimated time remaining: 00d 00h 36m
INFO 2025-07-05 18:20:49,547 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:20:49,547 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.9774016392727694, 'Losses/train_all_loss_mask': 0.0243065123911947, 'Losses/train_all_loss_dice': 1.0816946734984716, 'Losses/train_all_loss_iou': 0.37988492199607815, 'Losses/train_all_loss_class': 0.029691788235929075, 'Losses/train_all_core_loss': 1.9774016392727694, 'Trainer/where': 0.11979166666666667, 'Trainer/epoch': 4, 'Trainer/steps_train': 600}
INFO 2025-07-05 18:20:56,470 train_utils.py: 271: Train Epoch: [5][  0/120] | Batch Time: 5.93 (5.93) | Data Time: 5.09 (5.09) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 4.26e+00 (4.26e+00)
INFO 2025-07-05 18:21:01,435 train_utils.py: 271: Train Epoch: [5][ 10/120] | Batch Time: 0.34 (0.99) | Data Time: 0.00 (0.46) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 3.77e-01 (3.61e+00)
INFO 2025-07-05 18:21:06,973 train_utils.py: 271: Train Epoch: [5][ 20/120] | Batch Time: 0.57 (0.78) | Data Time: 0.00 (0.24) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 1.74e+00 (2.94e+00)
INFO 2025-07-05 18:21:11,595 train_utils.py: 271: Train Epoch: [5][ 30/120] | Batch Time: 0.35 (0.68) | Data Time: 0.00 (0.16) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 3.94e-01 (2.57e+00)
INFO 2025-07-05 18:21:17,328 train_utils.py: 271: Train Epoch: [5][ 40/120] | Batch Time: 0.40 (0.65) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 05m | Losses/train_all_loss: 4.66e-01 (2.50e+00)
INFO 2025-07-05 18:21:21,908 train_utils.py: 271: Train Epoch: [5][ 50/120] | Batch Time: 0.34 (0.62) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 2.17e-01 (2.17e+00)
INFO 2025-07-05 18:21:26,577 train_utils.py: 271: Train Epoch: [5][ 60/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 1.04e-01 (2.09e+00)
INFO 2025-07-05 18:21:31,652 train_utils.py: 271: Train Epoch: [5][ 70/120] | Batch Time: 0.64 (0.58) | Data Time: 0.01 (0.07) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 2.78e+00 (2.03e+00)
INFO 2025-07-05 18:21:37,168 train_utils.py: 271: Train Epoch: [5][ 80/120] | Batch Time: 0.60 (0.58) | Data Time: 0.01 (0.06) | Mem (GB): 6.00 (5.33/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 2.80e+00 (2.40e+00)
INFO 2025-07-05 18:21:42,020 train_utils.py: 271: Train Epoch: [5][ 90/120] | Batch Time: 0.36 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 5.02e-01 (2.37e+00)
INFO 2025-07-05 18:21:46,944 train_utils.py: 271: Train Epoch: [5][100/120] | Batch Time: 0.42 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 4.14e-01 (2.34e+00)
INFO 2025-07-05 18:21:51,977 train_utils.py: 271: Train Epoch: [5][110/120] | Batch Time: 0.35 (0.55) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 2.78e-01 (2.26e+00)
INFO 2025-07-05 18:21:57,198 trainer.py:1010: Estimated time remaining: 00d 00h 37m
INFO 2025-07-05 18:21:57,198 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:21:57,198 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.366342411314448, 'Losses/train_all_loss_mask': 0.03402084546687547, 'Losses/train_all_loss_dice': 1.2247023510436217, 'Losses/train_all_loss_iou': 0.43583991180639714, 'Losses/train_all_loss_class': 0.025383241558938608, 'Losses/train_all_core_loss': 2.366342411314448, 'Trainer/where': 0.14479166666666668, 'Trainer/epoch': 5, 'Trainer/steps_train': 720}
INFO 2025-07-05 18:22:04,283 train_utils.py: 271: Train Epoch: [6][  0/120] | Batch Time: 6.09 (6.09) | Data Time: 4.93 (4.93) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 1.31e+01 (1.31e+01)
INFO 2025-07-05 18:22:10,253 train_utils.py: 271: Train Epoch: [6][ 10/120] | Batch Time: 0.66 (1.10) | Data Time: 0.00 (0.45) | Mem (GB): 5.00 (5.64/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 1.00e+01 (5.37e+00)
INFO 2025-07-05 18:22:15,132 train_utils.py: 271: Train Epoch: [6][ 20/120] | Batch Time: 0.39 (0.81) | Data Time: 0.00 (0.24) | Mem (GB): 5.00 (5.48/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 1.03e-01 (3.88e+00)
INFO 2025-07-05 18:22:19,661 train_utils.py: 271: Train Epoch: [6][ 30/120] | Batch Time: 0.54 (0.69) | Data Time: 0.00 (0.16) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 06m | Losses/train_all_loss: 1.11e+00 (3.25e+00)
INFO 2025-07-05 18:22:25,007 train_utils.py: 271: Train Epoch: [6][ 40/120] | Batch Time: 0.37 (0.65) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 7.16e-02 (2.95e+00)
INFO 2025-07-05 18:22:30,877 train_utils.py: 271: Train Epoch: [6][ 50/120] | Batch Time: 0.66 (0.64) | Data Time: 0.00 (0.10) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 3.89e+00 (3.03e+00)
INFO 2025-07-05 18:22:36,105 train_utils.py: 271: Train Epoch: [6][ 60/120] | Batch Time: 0.56 (0.62) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.91e+00 (2.97e+00)
INFO 2025-07-05 18:22:42,310 train_utils.py: 271: Train Epoch: [6][ 70/120] | Batch Time: 0.39 (0.62) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 5.08e-01 (2.80e+00)
INFO 2025-07-05 18:22:47,894 train_utils.py: 271: Train Epoch: [6][ 80/120] | Batch Time: 0.44 (0.61) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.56e-01 (2.65e+00)
INFO 2025-07-05 18:22:52,324 train_utils.py: 271: Train Epoch: [6][ 90/120] | Batch Time: 0.39 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 1.38e+00 (2.47e+00)
INFO 2025-07-05 18:22:57,425 train_utils.py: 271: Train Epoch: [6][100/120] | Batch Time: 0.40 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.03e-01 (2.42e+00)
INFO 2025-07-05 18:23:02,763 train_utils.py: 271: Train Epoch: [6][110/120] | Batch Time: 0.35 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.20e-01 (2.36e+00)
INFO 2025-07-05 18:23:07,764 trainer.py:1010: Estimated time remaining: 00d 00h 37m
INFO 2025-07-05 18:23:07,764 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:23:07,764 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.38604967109859, 'Losses/train_all_loss_mask': 0.03298745843349025, 'Losses/train_all_loss_dice': 1.2738515456517538, 'Losses/train_all_loss_iou': 0.42449013017273196, 'Losses/train_all_loss_class': 0.027958819588366168, 'Losses/train_all_core_loss': 2.38604967109859, 'Trainer/where': 0.16979166666666667, 'Trainer/epoch': 6, 'Trainer/steps_train': 840}
INFO 2025-07-05 18:23:13,881 train_utils.py: 271: Train Epoch: [7][  0/120] | Batch Time: 5.20 (5.20) | Data Time: 4.50 (4.50) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.92e-01 (2.92e-01)
INFO 2025-07-05 18:23:19,011 train_utils.py: 271: Train Epoch: [7][ 10/120] | Batch Time: 0.71 (0.94) | Data Time: 0.00 (0.41) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 07m | Losses/train_all_loss: 2.81e+00 (1.29e+00)
INFO 2025-07-05 18:23:23,722 train_utils.py: 271: Train Epoch: [7][ 20/120] | Batch Time: 0.78 (0.72) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 2.09e+00 (1.31e+00)
INFO 2025-07-05 18:23:28,743 train_utils.py: 271: Train Epoch: [7][ 30/120] | Batch Time: 0.37 (0.65) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 1.53e-01 (1.55e+00)
INFO 2025-07-05 18:23:34,255 train_utils.py: 271: Train Epoch: [7][ 40/120] | Batch Time: 0.54 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 2.50e+00 (2.16e+00)
INFO 2025-07-05 18:23:39,381 train_utils.py: 271: Train Epoch: [7][ 50/120] | Batch Time: 0.51 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 6.84e-01 (2.01e+00)
INFO 2025-07-05 18:23:43,863 train_utils.py: 271: Train Epoch: [7][ 60/120] | Batch Time: 0.38 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 7.58e-02 (2.09e+00)
INFO 2025-07-05 18:23:48,759 train_utils.py: 271: Train Epoch: [7][ 70/120] | Batch Time: 0.74 (0.56) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.28/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 3.36e+00 (1.98e+00)
INFO 2025-07-05 18:23:54,976 train_utils.py: 271: Train Epoch: [7][ 80/120] | Batch Time: 0.74 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 1.17e+01 (2.16e+00)
INFO 2025-07-05 18:24:00,959 train_utils.py: 271: Train Epoch: [7][ 90/120] | Batch Time: 0.64 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 1.08e+01 (2.46e+00)
INFO 2025-07-05 18:24:05,518 train_utils.py: 271: Train Epoch: [7][100/120] | Batch Time: 0.38 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 1.47e-01 (2.43e+00)
INFO 2025-07-05 18:24:11,138 train_utils.py: 271: Train Epoch: [7][110/120] | Batch Time: 0.73 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 08m | Losses/train_all_loss: 3.36e+00 (2.42e+00)
INFO 2025-07-05 18:24:16,103 trainer.py:1010: Estimated time remaining: 00d 00h 35m
INFO 2025-07-05 18:24:16,104 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:24:16,104 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.366708703463276, 'Losses/train_all_loss_mask': 0.030152858901419676, 'Losses/train_all_loss_dice': 1.2959990185995898, 'Losses/train_all_loss_iou': 0.43581990218372085, 'Losses/train_all_loss_class': 0.031832621526397514, 'Losses/train_all_core_loss': 2.366708703463276, 'Trainer/where': 0.19479166666666667, 'Trainer/epoch': 7, 'Trainer/steps_train': 960}
INFO 2025-07-05 18:24:22,453 train_utils.py: 271: Train Epoch: [8][  0/120] | Batch Time: 5.37 (5.37) | Data Time: 4.68 (4.68) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 3.95e-01 (3.95e-01)
INFO 2025-07-05 18:24:28,268 train_utils.py: 271: Train Epoch: [8][ 10/120] | Batch Time: 0.66 (1.02) | Data Time: 0.00 (0.43) | Mem (GB): 6.00 (5.18/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 7.76e+00 (3.06e+00)
INFO 2025-07-05 18:24:32,889 train_utils.py: 271: Train Epoch: [8][ 20/120] | Batch Time: 0.70 (0.75) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.10/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 5.57e+00 (2.36e+00)
INFO 2025-07-05 18:24:38,415 train_utils.py: 271: Train Epoch: [8][ 30/120] | Batch Time: 0.83 (0.69) | Data Time: 0.00 (0.15) | Mem (GB): 6.00 (5.19/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 1.06e+00 (2.23e+00)
INFO 2025-07-05 18:24:42,651 train_utils.py: 271: Train Epoch: [8][ 40/120] | Batch Time: 0.42 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.15/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 4.12e-01 (1.86e+00)
INFO 2025-07-05 18:24:47,978 train_utils.py: 271: Train Epoch: [8][ 50/120] | Batch Time: 0.63 (0.61) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.16/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 1.40e+00 (1.91e+00)
INFO 2025-07-05 18:24:53,922 train_utils.py: 271: Train Epoch: [8][ 60/120] | Batch Time: 0.68 (0.60) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 3.00e+00 (1.98e+00)
INFO 2025-07-05 18:24:58,660 train_utils.py: 271: Train Epoch: [8][ 70/120] | Batch Time: 0.31 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.21/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 1.81e-01 (1.94e+00)
INFO 2025-07-05 18:25:03,799 train_utils.py: 271: Train Epoch: [8][ 80/120] | Batch Time: 0.69 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.23/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 5.28e+00 (1.90e+00)
INFO 2025-07-05 18:25:09,293 train_utils.py: 271: Train Epoch: [8][ 90/120] | Batch Time: 0.71 (0.57) | Data Time: 0.01 (0.05) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 7.51e+00 (2.18e+00)
INFO 2025-07-05 18:25:15,091 train_utils.py: 271: Train Epoch: [8][100/120] | Batch Time: 0.67 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 5.20e+00 (2.47e+00)
INFO 2025-07-05 18:25:20,917 train_utils.py: 271: Train Epoch: [8][110/120] | Batch Time: 0.69 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 09m | Losses/train_all_loss: 5.83e+00 (2.48e+00)
INFO 2025-07-05 18:25:27,164 trainer.py:1010: Estimated time remaining: 00d 00h 35m
INFO 2025-07-05 18:25:27,164 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:25:27,164 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.4334852903460464, 'Losses/train_all_loss_mask': 0.035970523782210266, 'Losses/train_all_loss_dice': 1.2435808983941874, 'Losses/train_all_loss_iou': 0.4192043317016214, 'Losses/train_all_loss_class': 0.051289566710875076, 'Losses/train_all_core_loss': 2.4334852903460464, 'Trainer/where': 0.21979166666666666, 'Trainer/epoch': 8, 'Trainer/steps_train': 1080}
INFO 2025-07-05 18:25:33,748 train_utils.py: 271: Train Epoch: [9][  0/120] | Batch Time: 5.55 (5.55) | Data Time: 4.57 (4.57) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 5.57e+00 (5.57e+00)
INFO 2025-07-05 18:25:38,578 train_utils.py: 271: Train Epoch: [9][ 10/120] | Batch Time: 0.35 (0.94) | Data Time: 0.00 (0.42) | Mem (GB): 5.00 (5.45/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 2.75e-01 (2.46e+00)
INFO 2025-07-05 18:25:43,494 train_utils.py: 271: Train Epoch: [9][ 20/120] | Batch Time: 0.37 (0.73) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 3.17e-01 (2.65e+00)
INFO 2025-07-05 18:25:48,315 train_utils.py: 271: Train Epoch: [9][ 30/120] | Batch Time: 0.34 (0.65) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 2.14e-01 (2.42e+00)
INFO 2025-07-05 18:25:54,251 train_utils.py: 271: Train Epoch: [9][ 40/120] | Batch Time: 0.72 (0.64) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.41/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 3.81e+00 (2.72e+00)
INFO 2025-07-05 18:25:59,946 train_utils.py: 271: Train Epoch: [9][ 50/120] | Batch Time: 0.33 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 1.58e-01 (2.79e+00)
INFO 2025-07-05 18:26:04,822 train_utils.py: 271: Train Epoch: [9][ 60/120] | Batch Time: 0.40 (0.60) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 4.02e-01 (2.91e+00)
INFO 2025-07-05 18:26:09,570 train_utils.py: 271: Train Epoch: [9][ 70/120] | Batch Time: 0.35 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 4.51e-01 (2.64e+00)
INFO 2025-07-05 18:26:15,015 train_utils.py: 271: Train Epoch: [9][ 80/120] | Batch Time: 0.40 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 4.46e-01 (2.61e+00)
INFO 2025-07-05 18:26:20,822 train_utils.py: 271: Train Epoch: [9][ 90/120] | Batch Time: 0.33 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 10m | Losses/train_all_loss: 9.71e-02 (2.64e+00)
INFO 2025-07-05 18:26:25,856 train_utils.py: 271: Train Epoch: [9][100/120] | Batch Time: 0.69 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 2.55e+00 (2.55e+00)
INFO 2025-07-05 18:26:31,007 train_utils.py: 271: Train Epoch: [9][110/120] | Batch Time: 0.36 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 1.02e+00 (2.46e+00)
INFO 2025-07-05 18:26:36,279 trainer.py:1010: Estimated time remaining: 00d 00h 33m
INFO 2025-07-05 18:26:36,279 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:26:36,279 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.4238954433550437, 'Losses/train_all_loss_mask': 0.03463119518225236, 'Losses/train_all_loss_dice': 1.3025114513933658, 'Losses/train_all_loss_iou': 0.41114080635597927, 'Losses/train_all_loss_class': 0.01761927464170488, 'Losses/train_all_core_loss': 2.4238954433550437, 'Trainer/where': 0.24479166666666666, 'Trainer/epoch': 9, 'Trainer/steps_train': 1200}
INFO 2025-07-05 18:26:42,473 train_utils.py: 271: Train Epoch: [10][  0/120] | Batch Time: 5.19 (5.19) | Data Time: 4.43 (4.43) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 5.68e-01 (5.68e-01)
INFO 2025-07-05 18:26:47,922 train_utils.py: 271: Train Epoch: [10][ 10/120] | Batch Time: 0.39 (0.97) | Data Time: 0.00 (0.40) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 8.28e-01 (1.58e+00)
INFO 2025-07-05 18:26:54,137 train_utils.py: 271: Train Epoch: [10][ 20/120] | Batch Time: 0.39 (0.80) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 5.31e-01 (2.34e+00)
INFO 2025-07-05 18:26:59,193 train_utils.py: 271: Train Epoch: [10][ 30/120] | Batch Time: 0.33 (0.71) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 5.23e-01 (2.38e+00)
INFO 2025-07-05 18:27:03,692 train_utils.py: 271: Train Epoch: [10][ 40/120] | Batch Time: 0.36 (0.64) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 5.29e-01 (2.10e+00)
INFO 2025-07-05 18:27:09,007 train_utils.py: 271: Train Epoch: [10][ 50/120] | Batch Time: 0.69 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 6.57e+00 (2.18e+00)
INFO 2025-07-05 18:27:13,693 train_utils.py: 271: Train Epoch: [10][ 60/120] | Batch Time: 0.74 (0.60) | Data Time: 0.01 (0.07) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 2.84e+00 (2.10e+00)
INFO 2025-07-05 18:27:19,703 train_utils.py: 271: Train Epoch: [10][ 70/120] | Batch Time: 0.88 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 11m | Losses/train_all_loss: 2.06e+00 (2.30e+00)
INFO 2025-07-05 18:27:25,031 train_utils.py: 271: Train Epoch: [10][ 80/120] | Batch Time: 0.57 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 2.00e+00 (2.18e+00)
INFO 2025-07-05 18:27:30,920 train_utils.py: 271: Train Epoch: [10][ 90/120] | Batch Time: 0.61 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 4.60e+00 (2.44e+00)
INFO 2025-07-05 18:27:36,012 train_utils.py: 271: Train Epoch: [10][100/120] | Batch Time: 0.72 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 1.04e+00 (2.30e+00)
INFO 2025-07-05 18:27:41,077 train_utils.py: 271: Train Epoch: [10][110/120] | Batch Time: 0.48 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 1.07e+00 (2.22e+00)
INFO 2025-07-05 18:27:46,170 trainer.py:1010: Estimated time remaining: 00d 00h 32m
INFO 2025-07-05 18:27:46,170 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:27:46,171 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.183039381727576, 'Losses/train_all_loss_mask': 0.028794176648564946, 'Losses/train_all_loss_dice': 1.179092023273309, 'Losses/train_all_loss_iou': 0.3865512266871519, 'Losses/train_all_loss_class': 0.04151257992246731, 'Losses/train_all_core_loss': 2.183039381727576, 'Trainer/where': 0.26979166666666665, 'Trainer/epoch': 10, 'Trainer/steps_train': 1320}
INFO 2025-07-05 18:27:53,090 train_utils.py: 271: Train Epoch: [11][  0/120] | Batch Time: 5.90 (5.90) | Data Time: 4.94 (4.94) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 2.59e+00 (2.59e+00)
INFO 2025-07-05 18:27:59,184 train_utils.py: 271: Train Epoch: [11][ 10/120] | Batch Time: 0.33 (1.09) | Data Time: 0.00 (0.45) | Mem (GB): 5.00 (5.45/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 7.08e-01 (2.00e+00)
INFO 2025-07-05 18:28:03,632 train_utils.py: 271: Train Epoch: [11][ 20/120] | Batch Time: 0.41 (0.78) | Data Time: 0.00 (0.24) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 3.85e-01 (1.38e+00)
INFO 2025-07-05 18:28:09,449 train_utils.py: 271: Train Epoch: [11][ 30/120] | Batch Time: 0.37 (0.72) | Data Time: 0.00 (0.16) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 5.44e-01 (1.65e+00)
INFO 2025-07-05 18:28:15,291 train_utils.py: 271: Train Epoch: [11][ 40/120] | Batch Time: 0.46 (0.69) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 4.83e-01 (1.91e+00)
INFO 2025-07-05 18:28:20,706 train_utils.py: 271: Train Epoch: [11][ 50/120] | Batch Time: 0.56 (0.66) | Data Time: 0.00 (0.10) | Mem (GB): 6.00 (5.41/6.00) | Time Elapsed: 00d 00h 12m | Losses/train_all_loss: 1.99e+00 (1.91e+00)
INFO 2025-07-05 18:28:25,726 train_utils.py: 271: Train Epoch: [11][ 60/120] | Batch Time: 0.61 (0.63) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 2.06e+00 (1.87e+00)
INFO 2025-07-05 18:28:30,829 train_utils.py: 271: Train Epoch: [11][ 70/120] | Batch Time: 0.76 (0.61) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 6.01e+00 (2.06e+00)
INFO 2025-07-05 18:28:35,873 train_utils.py: 271: Train Epoch: [11][ 80/120] | Batch Time: 0.39 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 1.75e-01 (1.96e+00)
INFO 2025-07-05 18:28:41,685 train_utils.py: 271: Train Epoch: [11][ 90/120] | Batch Time: 0.51 (0.60) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 2.50e+00 (2.07e+00)
INFO 2025-07-05 18:28:46,724 train_utils.py: 271: Train Epoch: [11][100/120] | Batch Time: 0.73 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 1.48e+00 (2.00e+00)
INFO 2025-07-05 18:28:52,133 train_utils.py: 271: Train Epoch: [11][110/120] | Batch Time: 0.69 (0.59) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.36/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 7.19e+00 (2.06e+00)
INFO 2025-07-05 18:28:58,124 trainer.py:1010: Estimated time remaining: 00d 00h 32m
INFO 2025-07-05 18:28:58,124 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:28:58,125 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.144504037934045, 'Losses/train_all_loss_mask': 0.026993585514234534, 'Losses/train_all_loss_dice': 1.218996903548638, 'Losses/train_all_loss_iou': 0.3791372645840359, 'Losses/train_all_loss_class': 0.006498146863881023, 'Losses/train_all_core_loss': 2.144504037934045, 'Trainer/where': 0.2947916666666667, 'Trainer/epoch': 11, 'Trainer/steps_train': 1440}
INFO 2025-07-05 18:29:04,817 train_utils.py: 271: Train Epoch: [12][  0/120] | Batch Time: 5.66 (5.66) | Data Time: 4.88 (4.88) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 1.65e-01 (1.65e-01)
INFO 2025-07-05 18:29:10,103 train_utils.py: 271: Train Epoch: [12][ 10/120] | Batch Time: 0.33 (1.00) | Data Time: 0.00 (0.44) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 2.09e-01 (2.45e+00)
INFO 2025-07-05 18:29:15,036 train_utils.py: 271: Train Epoch: [12][ 20/120] | Batch Time: 0.69 (0.76) | Data Time: 0.00 (0.23) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 1.95e+00 (2.07e+00)
INFO 2025-07-05 18:29:19,487 train_utils.py: 271: Train Epoch: [12][ 30/120] | Batch Time: 0.34 (0.66) | Data Time: 0.00 (0.16) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 13m | Losses/train_all_loss: 6.78e-01 (1.89e+00)
INFO 2025-07-05 18:29:24,615 train_utils.py: 271: Train Epoch: [12][ 40/120] | Batch Time: 0.80 (0.62) | Data Time: 0.00 (0.12) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 1.55e+00 (2.19e+00)
INFO 2025-07-05 18:29:30,112 train_utils.py: 271: Train Epoch: [12][ 50/120] | Batch Time: 0.50 (0.61) | Data Time: 0.00 (0.10) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 7.13e-01 (2.21e+00)
INFO 2025-07-05 18:29:35,794 train_utils.py: 271: Train Epoch: [12][ 60/120] | Batch Time: 0.42 (0.60) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 3.14e-01 (2.53e+00)
INFO 2025-07-05 18:29:40,859 train_utils.py: 271: Train Epoch: [12][ 70/120] | Batch Time: 0.70 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 1.90e+00 (2.46e+00)
INFO 2025-07-05 18:29:46,225 train_utils.py: 271: Train Epoch: [12][ 80/120] | Batch Time: 0.43 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 3.61e-01 (2.38e+00)
INFO 2025-07-05 18:29:51,037 train_utils.py: 271: Train Epoch: [12][ 90/120] | Batch Time: 0.73 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.36/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 4.63e+00 (2.27e+00)
INFO 2025-07-05 18:29:56,242 train_utils.py: 271: Train Epoch: [12][100/120] | Batch Time: 0.57 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 1.97e+00 (2.21e+00)
INFO 2025-07-05 18:30:02,079 train_utils.py: 271: Train Epoch: [12][110/120] | Batch Time: 0.36 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 7.04e-01 (2.35e+00)
INFO 2025-07-05 18:30:07,460 trainer.py:1010: Estimated time remaining: 00d 00h 30m
INFO 2025-07-05 18:30:07,460 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:30:07,460 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.334310535217325, 'Losses/train_all_loss_mask': 0.03149802623108186, 'Losses/train_all_loss_dice': 1.2701150919000308, 'Losses/train_all_loss_iou': 0.4135541810130235, 'Losses/train_all_loss_class': 0.020680727860235493, 'Losses/train_all_core_loss': 2.334310535217325, 'Trainer/where': 0.31979166666666664, 'Trainer/epoch': 12, 'Trainer/steps_train': 1560}
INFO 2025-07-05 18:30:13,542 train_utils.py: 271: Train Epoch: [13][  0/120] | Batch Time: 5.15 (5.15) | Data Time: 4.30 (4.30) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 3.17e-01 (3.17e-01)
INFO 2025-07-05 18:30:18,162 train_utils.py: 271: Train Epoch: [13][ 10/120] | Batch Time: 0.33 (0.89) | Data Time: 0.00 (0.39) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 14m | Losses/train_all_loss: 1.75e-01 (1.51e+00)
INFO 2025-07-05 18:30:23,610 train_utils.py: 271: Train Epoch: [13][ 20/120] | Batch Time: 0.68 (0.72) | Data Time: 0.00 (0.21) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 3.14e+00 (1.57e+00)
INFO 2025-07-05 18:30:27,507 train_utils.py: 271: Train Epoch: [13][ 30/120] | Batch Time: 0.37 (0.62) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 1.64e-01 (1.16e+00)
INFO 2025-07-05 18:30:33,014 train_utils.py: 271: Train Epoch: [13][ 40/120] | Batch Time: 0.37 (0.60) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.20/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 1.25e-01 (1.44e+00)
INFO 2025-07-05 18:30:38,135 train_utils.py: 271: Train Epoch: [13][ 50/120] | Batch Time: 0.63 (0.58) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 8.72e+00 (1.89e+00)
INFO 2025-07-05 18:30:43,643 train_utils.py: 271: Train Epoch: [13][ 60/120] | Batch Time: 0.70 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.25/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 5.36e+00 (1.97e+00)
INFO 2025-07-05 18:30:48,543 train_utils.py: 271: Train Epoch: [13][ 70/120] | Batch Time: 0.36 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 2.01e-01 (1.86e+00)
INFO 2025-07-05 18:30:54,131 train_utils.py: 271: Train Epoch: [13][ 80/120] | Batch Time: 0.73 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 5.07e+00 (1.87e+00)
INFO 2025-07-05 18:30:59,362 train_utils.py: 271: Train Epoch: [13][ 90/120] | Batch Time: 0.34 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 2.33e-01 (1.97e+00)
INFO 2025-07-05 18:31:04,953 train_utils.py: 271: Train Epoch: [13][100/120] | Batch Time: 0.73 (0.56) | Data Time: 0.01 (0.04) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 1.69e+00 (2.11e+00)
INFO 2025-07-05 18:31:10,237 train_utils.py: 271: Train Epoch: [13][110/120] | Batch Time: 0.69 (0.56) | Data Time: 0.01 (0.04) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 3.44e+00 (2.07e+00)
INFO 2025-07-05 18:31:14,714 trainer.py:1010: Estimated time remaining: 00d 00h 28m
INFO 2025-07-05 18:31:14,714 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:31:14,714 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.042584383363525, 'Losses/train_all_loss_mask': 0.030224118515616284, 'Losses/train_all_loss_dice': 1.0608415899177392, 'Losses/train_all_loss_iou': 0.3412475456095611, 'Losses/train_all_loss_class': 0.03601289557307912, 'Losses/train_all_core_loss': 2.042584383363525, 'Trainer/where': 0.34479166666666666, 'Trainer/epoch': 13, 'Trainer/steps_train': 1680}
INFO 2025-07-05 18:31:20,968 train_utils.py: 271: Train Epoch: [14][  0/120] | Batch Time: 5.31 (5.31) | Data Time: 4.60 (4.60) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 15m | Losses/train_all_loss: 4.12e-01 (4.12e-01)
INFO 2025-07-05 18:31:26,045 train_utils.py: 271: Train Epoch: [14][ 10/120] | Batch Time: 0.34 (0.94) | Data Time: 0.00 (0.42) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 2.53e-01 (1.19e+00)
INFO 2025-07-05 18:31:31,144 train_utils.py: 271: Train Epoch: [14][ 20/120] | Batch Time: 0.56 (0.74) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 9.80e-01 (1.51e+00)
INFO 2025-07-05 18:31:35,875 train_utils.py: 271: Train Epoch: [14][ 30/120] | Batch Time: 0.33 (0.65) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 6.19e-01 (1.55e+00)
INFO 2025-07-05 18:31:41,244 train_utils.py: 271: Train Epoch: [14][ 40/120] | Batch Time: 0.72 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 4.88e+00 (1.55e+00)
INFO 2025-07-05 18:31:46,275 train_utils.py: 271: Train Epoch: [14][ 50/120] | Batch Time: 0.35 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 1.32e+00 (1.68e+00)
INFO 2025-07-05 18:31:52,292 train_utils.py: 271: Train Epoch: [14][ 60/120] | Batch Time: 0.67 (0.60) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 4.16e+00 (1.94e+00)
INFO 2025-07-05 18:31:58,353 train_utils.py: 271: Train Epoch: [14][ 70/120] | Batch Time: 0.36 (0.60) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 9.32e-01 (2.16e+00)
INFO 2025-07-05 18:32:03,035 train_utils.py: 271: Train Epoch: [14][ 80/120] | Batch Time: 0.40 (0.58) | Data Time: 0.01 (0.06) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 5.70e-01 (2.27e+00)
INFO 2025-07-05 18:32:08,215 train_utils.py: 271: Train Epoch: [14][ 90/120] | Batch Time: 0.34 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 1.32e-01 (2.22e+00)
INFO 2025-07-05 18:32:13,193 train_utils.py: 271: Train Epoch: [14][100/120] | Batch Time: 0.38 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 1.37e-01 (2.10e+00)
INFO 2025-07-05 18:32:18,410 train_utils.py: 271: Train Epoch: [14][110/120] | Batch Time: 0.34 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 16m | Losses/train_all_loss: 3.73e-01 (2.14e+00)
INFO 2025-07-05 18:32:23,256 trainer.py:1010: Estimated time remaining: 00d 00h 27m
INFO 2025-07-05 18:32:23,256 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:32:23,256 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.0583070492371918, 'Losses/train_all_loss_mask': 0.03129609782287541, 'Losses/train_all_loss_dice': 1.0856436548133692, 'Losses/train_all_loss_iou': 0.33418638895576197, 'Losses/train_all_loss_class': 0.012555035318670586, 'Losses/train_all_core_loss': 2.0583070492371918, 'Trainer/where': 0.36979166666666663, 'Trainer/epoch': 14, 'Trainer/steps_train': 1800}
INFO 2025-07-05 18:32:29,417 train_utils.py: 271: Train Epoch: [15][  0/120] | Batch Time: 5.18 (5.18) | Data Time: 4.47 (4.47) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 2.01e-01 (2.01e-01)
INFO 2025-07-05 18:32:34,746 train_utils.py: 271: Train Epoch: [15][ 10/120] | Batch Time: 0.49 (0.96) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 1.02e+00 (2.04e+00)
INFO 2025-07-05 18:32:39,760 train_utils.py: 271: Train Epoch: [15][ 20/120] | Batch Time: 0.54 (0.74) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.43/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 6.47e-01 (2.23e+00)
INFO 2025-07-05 18:32:45,003 train_utils.py: 271: Train Epoch: [15][ 30/120] | Batch Time: 0.69 (0.67) | Data Time: 0.00 (0.14) | Mem (GB): 6.00 (5.42/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 1.22e+00 (2.21e+00)
INFO 2025-07-05 18:32:51,122 train_utils.py: 271: Train Epoch: [15][ 40/120] | Batch Time: 0.71 (0.66) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.44/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 8.64e+00 (2.44e+00)
INFO 2025-07-05 18:32:56,299 train_utils.py: 271: Train Epoch: [15][ 50/120] | Batch Time: 0.50 (0.63) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.41/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 5.24e+00 (2.33e+00)
INFO 2025-07-05 18:33:00,956 train_utils.py: 271: Train Epoch: [15][ 60/120] | Batch Time: 0.37 (0.60) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 4.01e-01 (2.17e+00)
INFO 2025-07-05 18:33:05,999 train_utils.py: 271: Train Epoch: [15][ 70/120] | Batch Time: 0.53 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 1.10e+00 (2.22e+00)
INFO 2025-07-05 18:33:11,888 train_utils.py: 271: Train Epoch: [15][ 80/120] | Batch Time: 0.70 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.37/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 1.74e+00 (2.60e+00)
INFO 2025-07-05 18:33:16,833 train_utils.py: 271: Train Epoch: [15][ 90/120] | Batch Time: 0.32 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 2.35e-01 (2.54e+00)
INFO 2025-07-05 18:33:21,540 train_utils.py: 271: Train Epoch: [15][100/120] | Batch Time: 0.39 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 17m | Losses/train_all_loss: 6.67e-01 (2.46e+00)
INFO 2025-07-05 18:33:26,613 train_utils.py: 271: Train Epoch: [15][110/120] | Batch Time: 0.67 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 1.49e+00 (2.52e+00)
INFO 2025-07-05 18:33:31,579 trainer.py:1010: Estimated time remaining: 00d 00h 26m
INFO 2025-07-05 18:33:31,579 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:33:31,579 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.466925892358025, 'Losses/train_all_loss_mask': 0.031008900228577354, 'Losses/train_all_loss_dice': 1.336791791766882, 'Losses/train_all_loss_iou': 0.4800030954609004, 'Losses/train_all_loss_class': 0.029953014530239366, 'Losses/train_all_core_loss': 2.466925892358025, 'Trainer/where': 0.39479166666666665, 'Trainer/epoch': 15, 'Trainer/steps_train': 1920}
INFO 2025-07-05 18:33:38,017 train_utils.py: 271: Train Epoch: [16][  0/120] | Batch Time: 5.52 (5.52) | Data Time: 4.51 (4.51) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 1.80e+00 (1.80e+00)
INFO 2025-07-05 18:33:42,705 train_utils.py: 271: Train Epoch: [16][ 10/120] | Batch Time: 0.33 (0.93) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 8.31e-01 (2.07e+00)
INFO 2025-07-05 18:33:47,478 train_utils.py: 271: Train Epoch: [16][ 20/120] | Batch Time: 0.55 (0.71) | Data Time: 0.00 (0.22) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 7.42e+00 (2.14e+00)
INFO 2025-07-05 18:33:52,039 train_utils.py: 271: Train Epoch: [16][ 30/120] | Batch Time: 0.32 (0.63) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 9.67e-02 (1.83e+00)
INFO 2025-07-05 18:33:56,720 train_utils.py: 271: Train Epoch: [16][ 40/120] | Batch Time: 0.55 (0.59) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 1.42e+00 (1.95e+00)
INFO 2025-07-05 18:34:02,164 train_utils.py: 271: Train Epoch: [16][ 50/120] | Batch Time: 0.37 (0.58) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 2.18e-01 (2.15e+00)
INFO 2025-07-05 18:34:07,125 train_utils.py: 271: Train Epoch: [16][ 60/120] | Batch Time: 0.57 (0.57) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.33/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 2.36e+00 (2.11e+00)
INFO 2025-07-05 18:34:12,405 train_utils.py: 271: Train Epoch: [16][ 70/120] | Batch Time: 0.38 (0.56) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 2.14e-01 (2.19e+00)
INFO 2025-07-05 18:34:17,690 train_utils.py: 271: Train Epoch: [16][ 80/120] | Batch Time: 0.40 (0.56) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 18m | Losses/train_all_loss: 6.17e-01 (2.06e+00)
INFO 2025-07-05 18:34:22,992 train_utils.py: 271: Train Epoch: [16][ 90/120] | Batch Time: 0.33 (0.55) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 2.34e-01 (2.00e+00)
INFO 2025-07-05 18:34:27,985 train_utils.py: 271: Train Epoch: [16][100/120] | Batch Time: 0.38 (0.55) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 1.44e-01 (1.96e+00)
INFO 2025-07-05 18:34:33,225 train_utils.py: 271: Train Epoch: [16][110/120] | Batch Time: 0.54 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 2.09e+00 (1.96e+00)
INFO 2025-07-05 18:34:37,413 trainer.py:1010: Estimated time remaining: 00d 00h 24m
INFO 2025-07-05 18:34:37,413 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:34:37,413 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.862687770773967, 'Losses/train_all_loss_mask': 0.025088852923364036, 'Losses/train_all_loss_dice': 0.9898686361809571, 'Losses/train_all_loss_iou': 0.32419325653463604, 'Losses/train_all_loss_class': 0.04684883935071108, 'Losses/train_all_core_loss': 1.862687770773967, 'Trainer/where': 0.4197916666666667, 'Trainer/epoch': 16, 'Trainer/steps_train': 2040}
INFO 2025-07-05 18:34:43,725 train_utils.py: 271: Train Epoch: [17][  0/120] | Batch Time: 5.36 (5.36) | Data Time: 4.48 (4.48) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 2.11e+00 (2.11e+00)
INFO 2025-07-05 18:34:48,848 train_utils.py: 271: Train Epoch: [17][ 10/120] | Batch Time: 0.66 (0.95) | Data Time: 0.00 (0.41) | Mem (GB): 6.00 (5.36/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 1.22e+00 (1.09e+00)
INFO 2025-07-05 18:34:54,523 train_utils.py: 271: Train Epoch: [17][ 20/120] | Batch Time: 0.39 (0.77) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.43/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 4.93e-01 (1.58e+00)
INFO 2025-07-05 18:34:59,639 train_utils.py: 271: Train Epoch: [17][ 30/120] | Batch Time: 0.61 (0.69) | Data Time: 0.00 (0.15) | Mem (GB): 6.00 (5.45/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 3.70e+00 (2.07e+00)
INFO 2025-07-05 18:35:04,694 train_utils.py: 271: Train Epoch: [17][ 40/120] | Batch Time: 0.38 (0.64) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.41/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 2.87e-01 (2.11e+00)
INFO 2025-07-05 18:35:09,881 train_utils.py: 271: Train Epoch: [17][ 50/120] | Batch Time: 0.74 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 1.75e+00 (1.89e+00)
INFO 2025-07-05 18:35:14,570 train_utils.py: 271: Train Epoch: [17][ 60/120] | Batch Time: 0.42 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 6.66e-01 (1.80e+00)
INFO 2025-07-05 18:35:20,290 train_utils.py: 271: Train Epoch: [17][ 70/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 19m | Losses/train_all_loss: 7.79e-02 (1.81e+00)
INFO 2025-07-05 18:35:25,923 train_utils.py: 271: Train Epoch: [17][ 80/120] | Batch Time: 0.41 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.25e+00 (1.92e+00)
INFO 2025-07-05 18:35:31,837 train_utils.py: 271: Train Epoch: [17][ 90/120] | Batch Time: 0.35 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.29e-01 (1.93e+00)
INFO 2025-07-05 18:35:37,105 train_utils.py: 271: Train Epoch: [17][100/120] | Batch Time: 0.62 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.28/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 3.71e+00 (2.00e+00)
INFO 2025-07-05 18:35:42,329 train_utils.py: 271: Train Epoch: [17][110/120] | Batch Time: 0.71 (0.58) | Data Time: 0.01 (0.04) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.80e+00 (1.99e+00)
INFO 2025-07-05 18:35:47,274 trainer.py:1010: Estimated time remaining: 00d 00h 25m
INFO 2025-07-05 18:35:47,274 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:35:47,274 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.9709349286432067, 'Losses/train_all_loss_mask': 0.02754998801004452, 'Losses/train_all_loss_dice': 1.0422779006262621, 'Losses/train_all_loss_iou': 0.3485159779607784, 'Losses/train_all_loss_class': 0.029141308252337694, 'Losses/train_all_core_loss': 1.9709349286432067, 'Trainer/where': 0.4447916666666667, 'Trainer/epoch': 17, 'Trainer/steps_train': 2160}
INFO 2025-07-05 18:35:55,408 train_utils.py: 271: Train Epoch: [18][  0/120] | Batch Time: 7.03 (7.03) | Data Time: 5.70 (5.70) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.72e+00 (1.72e+00)
INFO 2025-07-05 18:36:01,713 train_utils.py: 271: Train Epoch: [18][ 10/120] | Batch Time: 0.73 (1.21) | Data Time: 0.00 (0.52) | Mem (GB): 5.00 (5.45/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 2.31e+00 (2.40e+00)
INFO 2025-07-05 18:36:07,791 train_utils.py: 271: Train Epoch: [18][ 20/120] | Batch Time: 0.94 (0.92) | Data Time: 0.00 (0.27) | Mem (GB): 5.00 (5.43/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.49e+00 (2.72e+00)
INFO 2025-07-05 18:36:12,167 train_utils.py: 271: Train Epoch: [18][ 30/120] | Batch Time: 0.35 (0.77) | Data Time: 0.00 (0.18) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 2.41e-01 (1.97e+00)
INFO 2025-07-05 18:36:17,348 train_utils.py: 271: Train Epoch: [18][ 40/120] | Batch Time: 0.75 (0.71) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 20m | Losses/train_all_loss: 1.76e+00 (2.18e+00)
INFO 2025-07-05 18:36:22,530 train_utils.py: 271: Train Epoch: [18][ 50/120] | Batch Time: 0.53 (0.67) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 1.69e+00 (2.15e+00)
INFO 2025-07-05 18:36:27,673 train_utils.py: 271: Train Epoch: [18][ 60/120] | Batch Time: 0.40 (0.64) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 1.87e-01 (2.21e+00)
INFO 2025-07-05 18:36:33,304 train_utils.py: 271: Train Epoch: [18][ 70/120] | Batch Time: 0.67 (0.63) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.28/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 2.38e+00 (2.15e+00)
INFO 2025-07-05 18:36:38,378 train_utils.py: 271: Train Epoch: [18][ 80/120] | Batch Time: 0.41 (0.62) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 1.37e+00 (2.25e+00)
INFO 2025-07-05 18:36:43,525 train_utils.py: 271: Train Epoch: [18][ 90/120] | Batch Time: 0.53 (0.61) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 3.67e+00 (2.33e+00)
INFO 2025-07-05 18:36:48,930 train_utils.py: 271: Train Epoch: [18][100/120] | Batch Time: 0.40 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 4.25e-01 (2.31e+00)
INFO 2025-07-05 18:36:53,603 train_utils.py: 271: Train Epoch: [18][110/120] | Batch Time: 0.49 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 9.90e-01 (2.27e+00)
INFO 2025-07-05 18:36:59,343 trainer.py:1010: Estimated time remaining: 00d 00h 24m
INFO 2025-07-05 18:36:59,343 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:36:59,343 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.4303894020617007, 'Losses/train_all_loss_mask': 0.038807407797139606, 'Losses/train_all_loss_dice': 1.180988221615553, 'Losses/train_all_loss_iou': 0.4294175492696619, 'Losses/train_all_loss_class': 0.04383547483036333, 'Losses/train_all_core_loss': 2.4303894020617007, 'Trainer/where': 0.4697916666666667, 'Trainer/epoch': 18, 'Trainer/steps_train': 2280}
INFO 2025-07-05 18:37:06,494 train_utils.py: 271: Train Epoch: [19][  0/120] | Batch Time: 6.11 (6.11) | Data Time: 5.17 (5.17) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 6.75e+00 (6.75e+00)
INFO 2025-07-05 18:37:11,643 train_utils.py: 271: Train Epoch: [19][ 10/120] | Batch Time: 0.32 (1.02) | Data Time: 0.00 (0.47) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 2.74e-01 (2.67e+00)
INFO 2025-07-05 18:37:16,555 train_utils.py: 271: Train Epoch: [19][ 20/120] | Batch Time: 0.39 (0.77) | Data Time: 0.00 (0.25) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 21m | Losses/train_all_loss: 2.08e-01 (2.21e+00)
INFO 2025-07-05 18:37:22,313 train_utils.py: 271: Train Epoch: [19][ 30/120] | Batch Time: 0.68 (0.71) | Data Time: 0.00 (0.17) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 1.69e+00 (2.55e+00)
INFO 2025-07-05 18:37:27,615 train_utils.py: 271: Train Epoch: [19][ 40/120] | Batch Time: 0.69 (0.66) | Data Time: 0.00 (0.13) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 1.93e+00 (2.41e+00)
INFO 2025-07-05 18:37:32,836 train_utils.py: 271: Train Epoch: [19][ 50/120] | Batch Time: 0.63 (0.64) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 1.08e+00 (2.35e+00)
INFO 2025-07-05 18:37:37,610 train_utils.py: 271: Train Epoch: [19][ 60/120] | Batch Time: 0.61 (0.61) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.33/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 2.24e+00 (2.11e+00)
INFO 2025-07-05 18:37:42,239 train_utils.py: 271: Train Epoch: [19][ 70/120] | Batch Time: 0.34 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 5.11e-01 (2.02e+00)
INFO 2025-07-05 18:37:48,483 train_utils.py: 271: Train Epoch: [19][ 80/120] | Batch Time: 0.72 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 3.16e+00 (2.17e+00)
INFO 2025-07-05 18:37:54,537 train_utils.py: 271: Train Epoch: [19][ 90/120] | Batch Time: 0.52 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 3.77e+00 (2.31e+00)
INFO 2025-07-05 18:37:59,504 train_utils.py: 271: Train Epoch: [19][100/120] | Batch Time: 0.39 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 7.62e-01 (2.24e+00)
INFO 2025-07-05 18:38:04,664 train_utils.py: 271: Train Epoch: [19][110/120] | Batch Time: 0.34 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 4.09e-01 (2.21e+00)
INFO 2025-07-05 18:38:09,776 trainer.py:1010: Estimated time remaining: 00d 00h 22m
INFO 2025-07-05 18:38:09,776 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:38:09,776 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.300846313002209, 'Losses/train_all_loss_mask': 0.032100015674950556, 'Losses/train_all_loss_dice': 1.1920806256433327, 'Losses/train_all_loss_iou': 0.44627196931978685, 'Losses/train_all_loss_class': 0.020493433888471677, 'Losses/train_all_core_loss': 2.300846313002209, 'Trainer/where': 0.4947916666666667, 'Trainer/epoch': 19, 'Trainer/steps_train': 2400}
INFO 2025-07-05 18:38:16,039 train_utils.py: 271: Train Epoch: [20][  0/120] | Batch Time: 5.33 (5.33) | Data Time: 4.54 (4.54) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 1.43e-01 (1.43e-01)
INFO 2025-07-05 18:38:20,956 train_utils.py: 271: Train Epoch: [20][ 10/120] | Batch Time: 0.33 (0.93) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 22m | Losses/train_all_loss: 3.64e-01 (1.65e+00)
INFO 2025-07-05 18:38:26,384 train_utils.py: 271: Train Epoch: [20][ 20/120] | Batch Time: 0.57 (0.75) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 2.86e+00 (2.20e+00)
INFO 2025-07-05 18:38:31,451 train_utils.py: 271: Train Epoch: [20][ 30/120] | Batch Time: 0.34 (0.67) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 7.21e-01 (2.11e+00)
INFO 2025-07-05 18:38:37,572 train_utils.py: 271: Train Epoch: [20][ 40/120] | Batch Time: 0.72 (0.66) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 6.61e+00 (2.30e+00)
INFO 2025-07-05 18:38:42,607 train_utils.py: 271: Train Epoch: [20][ 50/120] | Batch Time: 0.33 (0.63) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 1.62e+00 (2.12e+00)
INFO 2025-07-05 18:38:48,487 train_utils.py: 271: Train Epoch: [20][ 60/120] | Batch Time: 0.40 (0.62) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 3.53e-01 (2.32e+00)
INFO 2025-07-05 18:38:53,958 train_utils.py: 271: Train Epoch: [20][ 70/120] | Batch Time: 0.60 (0.61) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.32/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 4.32e+00 (2.41e+00)
INFO 2025-07-05 18:38:58,903 train_utils.py: 271: Train Epoch: [20][ 80/120] | Batch Time: 0.41 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 2.71e-01 (2.50e+00)
INFO 2025-07-05 18:39:04,725 train_utils.py: 271: Train Epoch: [20][ 90/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 1.52e-01 (2.54e+00)
INFO 2025-07-05 18:39:09,926 train_utils.py: 271: Train Epoch: [20][100/120] | Batch Time: 0.69 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.37/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 1.21e+01 (2.60e+00)
INFO 2025-07-05 18:39:15,152 train_utils.py: 271: Train Epoch: [20][110/120] | Batch Time: 0.33 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 23m | Losses/train_all_loss: 4.25e-01 (2.55e+00)
INFO 2025-07-05 18:39:20,348 trainer.py:1010: Estimated time remaining: 00d 00h 21m
INFO 2025-07-05 18:39:20,348 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:39:20,348 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.51729441297551, 'Losses/train_all_loss_mask': 0.03687725462368689, 'Losses/train_all_loss_dice': 1.3142997150619824, 'Losses/train_all_loss_iou': 0.4527075247722678, 'Losses/train_all_loss_class': 0.012742082614355848, 'Losses/train_all_core_loss': 2.51729441297551, 'Trainer/where': 0.5197916666666667, 'Trainer/epoch': 20, 'Trainer/steps_train': 2520}
INFO 2025-07-05 18:39:27,343 train_utils.py: 271: Train Epoch: [21][  0/120] | Batch Time: 6.03 (6.03) | Data Time: 5.15 (5.15) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 9.39e-01 (9.39e-01)
INFO 2025-07-05 18:39:32,767 train_utils.py: 271: Train Epoch: [21][ 10/120] | Batch Time: 0.66 (1.04) | Data Time: 0.00 (0.47) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 9.41e-01 (1.62e+00)
INFO 2025-07-05 18:39:37,993 train_utils.py: 271: Train Epoch: [21][ 20/120] | Batch Time: 0.55 (0.79) | Data Time: 0.00 (0.25) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 6.24e+00 (2.03e+00)
INFO 2025-07-05 18:39:43,543 train_utils.py: 271: Train Epoch: [21][ 30/120] | Batch Time: 0.34 (0.72) | Data Time: 0.00 (0.17) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 2.37e-01 (2.18e+00)
INFO 2025-07-05 18:39:49,331 train_utils.py: 271: Train Epoch: [21][ 40/120] | Batch Time: 0.38 (0.68) | Data Time: 0.00 (0.13) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 1.31e-01 (2.83e+00)
INFO 2025-07-05 18:39:54,639 train_utils.py: 271: Train Epoch: [21][ 50/120] | Batch Time: 0.51 (0.65) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 8.55e-01 (2.61e+00)
INFO 2025-07-05 18:40:00,170 train_utils.py: 271: Train Epoch: [21][ 60/120] | Batch Time: 0.43 (0.64) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 1.69e+00 (2.43e+00)
INFO 2025-07-05 18:40:05,901 train_utils.py: 271: Train Epoch: [21][ 70/120] | Batch Time: 0.34 (0.63) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 1.95e-01 (2.53e+00)
INFO 2025-07-05 18:40:11,463 train_utils.py: 271: Train Epoch: [21][ 80/120] | Batch Time: 0.69 (0.62) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.36/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 1.26e+01 (2.59e+00)
INFO 2025-07-05 18:40:16,590 train_utils.py: 271: Train Epoch: [21][ 90/120] | Batch Time: 0.63 (0.61) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 24m | Losses/train_all_loss: 5.73e+00 (2.55e+00)
INFO 2025-07-05 18:40:23,562 train_utils.py: 271: Train Epoch: [21][100/120] | Batch Time: 1.77 (0.62) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 1.00e+01 (2.75e+00)
INFO 2025-07-05 18:40:29,873 train_utils.py: 271: Train Epoch: [21][110/120] | Batch Time: 0.33 (0.62) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 1.76e-01 (2.73e+00)
INFO 2025-07-05 18:40:34,814 trainer.py:1010: Estimated time remaining: 00d 00h 21m
INFO 2025-07-05 18:40:34,814 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:40:34,814 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.64874205806603, 'Losses/train_all_loss_mask': 0.04108282777985248, 'Losses/train_all_loss_dice': 1.3618515096604824, 'Losses/train_all_loss_iou': 0.43742088251553163, 'Losses/train_all_loss_class': 0.027813122161902962, 'Losses/train_all_core_loss': 2.64874205806603, 'Trainer/where': 0.5447916666666667, 'Trainer/epoch': 21, 'Trainer/steps_train': 2640}
INFO 2025-07-05 18:40:41,328 train_utils.py: 271: Train Epoch: [22][  0/120] | Batch Time: 5.49 (5.49) | Data Time: 4.46 (4.46) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 2.84e+00 (2.84e+00)
INFO 2025-07-05 18:40:46,122 train_utils.py: 271: Train Epoch: [22][ 10/120] | Batch Time: 0.32 (0.93) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 1.44e-01 (1.91e+00)
INFO 2025-07-05 18:40:51,418 train_utils.py: 271: Train Epoch: [22][ 20/120] | Batch Time: 0.71 (0.74) | Data Time: 0.00 (0.21) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 1.87e+01 (2.76e+00)
INFO 2025-07-05 18:40:56,471 train_utils.py: 271: Train Epoch: [22][ 30/120] | Batch Time: 0.80 (0.67) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 8.65e-01 (2.32e+00)
INFO 2025-07-05 18:41:01,335 train_utils.py: 271: Train Epoch: [22][ 40/120] | Batch Time: 0.71 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.20/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 3.30e+00 (2.01e+00)
INFO 2025-07-05 18:41:06,835 train_utils.py: 271: Train Epoch: [22][ 50/120] | Batch Time: 0.37 (0.61) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 3.53e-01 (1.89e+00)
INFO 2025-07-05 18:41:11,336 train_utils.py: 271: Train Epoch: [22][ 60/120] | Batch Time: 0.41 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.20/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 2.14e-01 (1.75e+00)
INFO 2025-07-05 18:41:16,068 train_utils.py: 271: Train Epoch: [22][ 70/120] | Batch Time: 0.34 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 25m | Losses/train_all_loss: 2.10e+00 (1.73e+00)
INFO 2025-07-05 18:41:22,100 train_utils.py: 271: Train Epoch: [22][ 80/120] | Batch Time: 0.76 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 3.06e+00 (1.88e+00)
INFO 2025-07-05 18:41:26,683 train_utils.py: 271: Train Epoch: [22][ 90/120] | Batch Time: 0.74 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 1.21e+01 (1.86e+00)
INFO 2025-07-05 18:41:31,326 train_utils.py: 271: Train Epoch: [22][100/120] | Batch Time: 0.42 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 1.43e-01 (1.90e+00)
INFO 2025-07-05 18:41:36,683 train_utils.py: 271: Train Epoch: [22][110/120] | Batch Time: 0.34 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 5.38e-01 (2.04e+00)
INFO 2025-07-05 18:41:42,691 trainer.py:1010: Estimated time remaining: 00d 00h 18m
INFO 2025-07-05 18:41:42,692 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:41:42,692 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.142029039748013, 'Losses/train_all_loss_mask': 0.026095140956749675, 'Losses/train_all_loss_dice': 1.16144279713432, 'Losses/train_all_loss_iou': 0.3989665906061418, 'Losses/train_all_loss_class': 0.05971685710721128, 'Losses/train_all_core_loss': 2.142029039748013, 'Trainer/where': 0.5697916666666667, 'Trainer/epoch': 22, 'Trainer/steps_train': 2760}
INFO 2025-07-05 18:41:49,399 train_utils.py: 271: Train Epoch: [23][  0/120] | Batch Time: 5.68 (5.68) | Data Time: 4.57 (4.57) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 1.31e+01 (1.31e+01)
INFO 2025-07-05 18:41:53,846 train_utils.py: 271: Train Epoch: [23][ 10/120] | Batch Time: 0.35 (0.92) | Data Time: 0.00 (0.42) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 3.84e-01 (1.89e+00)
INFO 2025-07-05 18:41:58,432 train_utils.py: 271: Train Epoch: [23][ 20/120] | Batch Time: 0.38 (0.70) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 3.15e-01 (1.59e+00)
INFO 2025-07-05 18:42:04,144 train_utils.py: 271: Train Epoch: [23][ 30/120] | Batch Time: 0.58 (0.66) | Data Time: 0.00 (0.15) | Mem (GB): 6.00 (5.19/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 3.75e+00 (1.64e+00)
INFO 2025-07-05 18:42:09,402 train_utils.py: 271: Train Epoch: [23][ 40/120] | Batch Time: 0.72 (0.63) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 9.34e+00 (1.72e+00)
INFO 2025-07-05 18:42:14,143 train_utils.py: 271: Train Epoch: [23][ 50/120] | Batch Time: 0.35 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 2.65e-01 (1.81e+00)
INFO 2025-07-05 18:42:19,455 train_utils.py: 271: Train Epoch: [23][ 60/120] | Batch Time: 0.70 (0.59) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 26m | Losses/train_all_loss: 2.54e+00 (1.80e+00)
INFO 2025-07-05 18:42:24,720 train_utils.py: 271: Train Epoch: [23][ 70/120] | Batch Time: 0.38 (0.58) | Data Time: 0.01 (0.06) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.28e-01 (1.84e+00)
INFO 2025-07-05 18:42:30,043 train_utils.py: 271: Train Epoch: [23][ 80/120] | Batch Time: 0.38 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.38e-01 (1.84e+00)
INFO 2025-07-05 18:42:35,264 train_utils.py: 271: Train Epoch: [23][ 90/120] | Batch Time: 0.33 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.12e-01 (1.86e+00)
INFO 2025-07-05 18:42:40,968 train_utils.py: 271: Train Epoch: [23][100/120] | Batch Time: 0.64 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.08e+01 (2.11e+00)
INFO 2025-07-05 18:42:46,123 train_utils.py: 271: Train Epoch: [23][110/120] | Batch Time: 0.70 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.27e+00 (2.15e+00)
INFO 2025-07-05 18:42:50,624 trainer.py:1010: Estimated time remaining: 00d 00h 17m
INFO 2025-07-05 18:42:50,624 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:42:50,624 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.1555678909644485, 'Losses/train_all_loss_mask': 0.031195951865326303, 'Losses/train_all_loss_dice': 1.1464546253283818, 'Losses/train_all_loss_iou': 0.38088473622531943, 'Losses/train_all_loss_class': 0.004309534708621262, 'Losses/train_all_core_loss': 2.1555678909644485, 'Trainer/where': 0.5947916666666667, 'Trainer/epoch': 23, 'Trainer/steps_train': 2880}
INFO 2025-07-05 18:42:56,887 train_utils.py: 271: Train Epoch: [24][  0/120] | Batch Time: 5.23 (5.23) | Data Time: 4.51 (4.51) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.76e-01 (1.76e-01)
INFO 2025-07-05 18:43:01,873 train_utils.py: 271: Train Epoch: [24][ 10/120] | Batch Time: 0.73 (0.93) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 3.20e+00 (1.93e+00)
INFO 2025-07-05 18:43:06,732 train_utils.py: 271: Train Epoch: [24][ 20/120] | Batch Time: 0.56 (0.72) | Data Time: 0.00 (0.22) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 2.84e+00 (1.91e+00)
INFO 2025-07-05 18:43:11,703 train_utils.py: 271: Train Epoch: [24][ 30/120] | Batch Time: 0.36 (0.65) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.42e-01 (2.09e+00)
INFO 2025-07-05 18:43:17,722 train_utils.py: 271: Train Epoch: [24][ 40/120] | Batch Time: 0.64 (0.64) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 27m | Losses/train_all_loss: 1.77e+00 (2.29e+00)
INFO 2025-07-05 18:43:22,357 train_utils.py: 271: Train Epoch: [24][ 50/120] | Batch Time: 0.36 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 2.80e-01 (2.23e+00)
INFO 2025-07-05 18:43:28,252 train_utils.py: 271: Train Epoch: [24][ 60/120] | Batch Time: 0.78 (0.60) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.33/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 4.69e+00 (2.37e+00)
INFO 2025-07-05 18:43:34,074 train_utils.py: 271: Train Epoch: [24][ 70/120] | Batch Time: 0.32 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 5.75e-02 (2.28e+00)
INFO 2025-07-05 18:43:38,683 train_utils.py: 271: Train Epoch: [24][ 80/120] | Batch Time: 0.37 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 3.09e-01 (2.32e+00)
INFO 2025-07-05 18:43:43,048 train_utils.py: 271: Train Epoch: [24][ 90/120] | Batch Time: 0.32 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 1.33e-01 (2.16e+00)
INFO 2025-07-05 18:43:49,508 train_utils.py: 271: Train Epoch: [24][100/120] | Batch Time: 0.68 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.33/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 4.79e+00 (2.31e+00)
INFO 2025-07-05 18:43:54,929 train_utils.py: 271: Train Epoch: [24][110/120] | Batch Time: 0.33 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 1.93e-01 (2.34e+00)
INFO 2025-07-05 18:44:01,037 trainer.py:1010: Estimated time remaining: 00d 00h 17m
INFO 2025-07-05 18:44:01,037 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:44:01,037 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.407767635490745, 'Losses/train_all_loss_mask': 0.03672806015965761, 'Losses/train_all_loss_dice': 1.2455466834207376, 'Losses/train_all_loss_iou': 0.41253970497831083, 'Losses/train_all_loss_class': 0.015120049011147784, 'Losses/train_all_core_loss': 2.407767635490745, 'Trainer/where': 0.6197916666666667, 'Trainer/epoch': 24, 'Trainer/steps_train': 3000}
INFO 2025-07-05 18:44:07,101 train_utils.py: 271: Train Epoch: [25][  0/120] | Batch Time: 5.11 (5.11) | Data Time: 4.37 (4.37) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 3.05e-01 (3.05e-01)
INFO 2025-07-05 18:44:12,492 train_utils.py: 271: Train Epoch: [25][ 10/120] | Batch Time: 0.69 (0.95) | Data Time: 0.00 (0.40) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 6.32e+00 (2.60e+00)
INFO 2025-07-05 18:44:17,551 train_utils.py: 271: Train Epoch: [25][ 20/120] | Batch Time: 0.40 (0.74) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 28m | Losses/train_all_loss: 2.13e-01 (2.62e+00)
INFO 2025-07-05 18:44:22,916 train_utils.py: 271: Train Epoch: [25][ 30/120] | Batch Time: 0.34 (0.67) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 1.80e+00 (2.62e+00)
INFO 2025-07-05 18:44:27,944 train_utils.py: 271: Train Epoch: [25][ 40/120] | Batch Time: 0.38 (0.63) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 3.29e-01 (2.40e+00)
INFO 2025-07-05 18:44:33,116 train_utils.py: 271: Train Epoch: [25][ 50/120] | Batch Time: 0.64 (0.61) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 6.32e+00 (2.20e+00)
INFO 2025-07-05 18:44:38,075 train_utils.py: 271: Train Epoch: [25][ 60/120] | Batch Time: 0.52 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 1.29e+00 (2.14e+00)
INFO 2025-07-05 18:44:43,643 train_utils.py: 271: Train Epoch: [25][ 70/120] | Batch Time: 0.64 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 2.19e+00 (2.14e+00)
INFO 2025-07-05 18:44:49,586 train_utils.py: 271: Train Epoch: [25][ 80/120] | Batch Time: 0.40 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 1.09e-01 (2.20e+00)
INFO 2025-07-05 18:44:54,931 train_utils.py: 271: Train Epoch: [25][ 90/120] | Batch Time: 0.39 (0.58) | Data Time: 0.01 (0.05) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 1.02e+00 (2.23e+00)
INFO 2025-07-05 18:44:59,666 train_utils.py: 271: Train Epoch: [25][100/120] | Batch Time: 0.45 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 2.66e-01 (2.13e+00)
INFO 2025-07-05 18:45:04,817 train_utils.py: 271: Train Epoch: [25][110/120] | Batch Time: 0.37 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 5.91e-01 (2.22e+00)
INFO 2025-07-05 18:45:10,275 trainer.py:1010: Estimated time remaining: 00d 00h 15m
INFO 2025-07-05 18:45:10,275 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:45:10,278 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.27483415066575, 'Losses/train_all_loss_mask': 0.029171586880693212, 'Losses/train_all_loss_dice': 1.2392993030448756, 'Losses/train_all_loss_iou': 0.4325245270311522, 'Losses/train_all_loss_class': 0.019578617016865488, 'Losses/train_all_core_loss': 2.27483415066575, 'Trainer/where': 0.6447916666666667, 'Trainer/epoch': 25, 'Trainer/steps_train': 3120}
INFO 2025-07-05 18:45:16,556 train_utils.py: 271: Train Epoch: [26][  0/120] | Batch Time: 5.34 (5.34) | Data Time: 4.32 (4.32) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 2.96e+00 (2.96e+00)
INFO 2025-07-05 18:45:21,718 train_utils.py: 271: Train Epoch: [26][ 10/120] | Batch Time: 0.32 (0.95) | Data Time: 0.00 (0.39) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 29m | Losses/train_all_loss: 2.29e-01 (1.80e+00)
INFO 2025-07-05 18:45:26,297 train_utils.py: 271: Train Epoch: [26][ 20/120] | Batch Time: 0.48 (0.72) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 2.00e-01 (2.21e+00)
INFO 2025-07-05 18:45:31,798 train_utils.py: 271: Train Epoch: [26][ 30/120] | Batch Time: 0.74 (0.66) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 1.47e+00 (2.28e+00)
INFO 2025-07-05 18:45:36,884 train_utils.py: 271: Train Epoch: [26][ 40/120] | Batch Time: 0.62 (0.63) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 2.90e+00 (2.17e+00)
INFO 2025-07-05 18:45:41,636 train_utils.py: 271: Train Epoch: [26][ 50/120] | Batch Time: 0.54 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 8.98e-01 (2.02e+00)
INFO 2025-07-05 18:45:46,741 train_utils.py: 271: Train Epoch: [26][ 60/120] | Batch Time: 0.40 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 7.30e-01 (1.95e+00)
INFO 2025-07-05 18:45:52,346 train_utils.py: 271: Train Epoch: [26][ 70/120] | Batch Time: 0.67 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 1.00e+01 (2.02e+00)
INFO 2025-07-05 18:45:57,099 train_utils.py: 271: Train Epoch: [26][ 80/120] | Batch Time: 0.37 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 1.59e-01 (2.12e+00)
INFO 2025-07-05 18:46:03,222 train_utils.py: 271: Train Epoch: [26][ 90/120] | Batch Time: 0.50 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 9.32e-01 (2.11e+00)
INFO 2025-07-05 18:46:07,976 train_utils.py: 271: Train Epoch: [26][100/120] | Batch Time: 0.40 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 1.68e-01 (2.02e+00)
INFO 2025-07-05 18:46:12,942 train_utils.py: 271: Train Epoch: [26][110/120] | Batch Time: 0.56 (0.56) | Data Time: 0.01 (0.04) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 30m | Losses/train_all_loss: 1.27e+00 (1.98e+00)
INFO 2025-07-05 18:46:18,118 trainer.py:1010: Estimated time remaining: 00d 00h 14m
INFO 2025-07-05 18:46:18,118 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:46:18,118 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.958559321053326, 'Losses/train_all_loss_mask': 0.02792083262029337, 'Losses/train_all_loss_dice': 1.0637074733773868, 'Losses/train_all_loss_iou': 0.330751636432251, 'Losses/train_all_loss_class': 0.005683566977919933, 'Losses/train_all_core_loss': 1.958559321053326, 'Trainer/where': 0.6697916666666667, 'Trainer/epoch': 26, 'Trainer/steps_train': 3240}
INFO 2025-07-05 18:46:24,820 train_utils.py: 271: Train Epoch: [27][  0/120] | Batch Time: 5.76 (5.76) | Data Time: 4.87 (4.87) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 1.33e+00 (1.33e+00)
INFO 2025-07-05 18:46:30,121 train_utils.py: 271: Train Epoch: [27][ 10/120] | Batch Time: 0.37 (1.01) | Data Time: 0.00 (0.44) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 1.03e+00 (2.04e+00)
INFO 2025-07-05 18:46:35,183 train_utils.py: 271: Train Epoch: [27][ 20/120] | Batch Time: 0.70 (0.77) | Data Time: 0.00 (0.23) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 3.60e+00 (1.63e+00)
INFO 2025-07-05 18:46:40,680 train_utils.py: 271: Train Epoch: [27][ 30/120] | Batch Time: 0.52 (0.70) | Data Time: 0.00 (0.16) | Mem (GB): 6.00 (5.29/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 1.56e+00 (1.75e+00)
INFO 2025-07-05 18:46:46,912 train_utils.py: 271: Train Epoch: [27][ 40/120] | Batch Time: 0.73 (0.68) | Data Time: 0.00 (0.12) | Mem (GB): 6.00 (5.32/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 2.61e+00 (1.93e+00)
INFO 2025-07-05 18:46:52,202 train_utils.py: 271: Train Epoch: [27][ 50/120] | Batch Time: 0.57 (0.65) | Data Time: 0.00 (0.10) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 2.77e+00 (1.78e+00)
INFO 2025-07-05 18:46:56,928 train_utils.py: 271: Train Epoch: [27][ 60/120] | Batch Time: 0.81 (0.62) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.30/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 7.84e+00 (1.77e+00)
INFO 2025-07-05 18:47:03,165 train_utils.py: 271: Train Epoch: [27][ 70/120] | Batch Time: 0.69 (0.62) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 8.08e-01 (1.79e+00)
INFO 2025-07-05 18:47:09,012 train_utils.py: 271: Train Epoch: [27][ 80/120] | Batch Time: 0.37 (0.62) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 7.88e-02 (1.98e+00)
INFO 2025-07-05 18:47:14,261 train_utils.py: 271: Train Epoch: [27][ 90/120] | Batch Time: 0.34 (0.61) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 1.53e-01 (1.99e+00)
INFO 2025-07-05 18:47:18,858 train_utils.py: 271: Train Epoch: [27][100/120] | Batch Time: 0.39 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 31m | Losses/train_all_loss: 2.23e-01 (1.85e+00)
INFO 2025-07-05 18:47:24,719 train_utils.py: 271: Train Epoch: [27][110/120] | Batch Time: 0.76 (0.59) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 4.81e+00 (2.13e+00)
INFO 2025-07-05 18:47:30,534 trainer.py:1010: Estimated time remaining: 00d 00h 14m
INFO 2025-07-05 18:47:30,534 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:47:30,534 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.20076492211471, 'Losses/train_all_loss_mask': 0.034560153125979313, 'Losses/train_all_loss_dice': 1.1385323223968347, 'Losses/train_all_loss_iou': 0.35715444602343877, 'Losses/train_all_loss_class': 0.013875146752692065, 'Losses/train_all_core_loss': 2.20076492211471, 'Trainer/where': 0.6947916666666667, 'Trainer/epoch': 27, 'Trainer/steps_train': 3360}
INFO 2025-07-05 18:47:37,096 train_utils.py: 271: Train Epoch: [28][  0/120] | Batch Time: 5.57 (5.57) | Data Time: 4.84 (4.84) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 3.77e-01 (3.77e-01)
INFO 2025-07-05 18:47:42,631 train_utils.py: 271: Train Epoch: [28][ 10/120] | Batch Time: 0.70 (1.01) | Data Time: 0.00 (0.44) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 3.48e+00 (2.66e+00)
INFO 2025-07-05 18:47:47,434 train_utils.py: 271: Train Epoch: [28][ 20/120] | Batch Time: 0.59 (0.76) | Data Time: 0.00 (0.23) | Mem (GB): 6.00 (5.14/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 4.42e+00 (2.13e+00)
INFO 2025-07-05 18:47:52,240 train_utils.py: 271: Train Epoch: [28][ 30/120] | Batch Time: 0.68 (0.67) | Data Time: 0.00 (0.16) | Mem (GB): 5.00 (5.13/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 2.44e+00 (1.74e+00)
INFO 2025-07-05 18:47:58,003 train_utils.py: 271: Train Epoch: [28][ 40/120] | Batch Time: 0.60 (0.65) | Data Time: 0.00 (0.12) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 2.28e+00 (2.15e+00)
INFO 2025-07-05 18:48:04,179 train_utils.py: 271: Train Epoch: [28][ 50/120] | Batch Time: 0.37 (0.64) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 4.00e-01 (2.10e+00)
INFO 2025-07-05 18:48:08,971 train_utils.py: 271: Train Epoch: [28][ 60/120] | Batch Time: 0.43 (0.61) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 3.77e-01 (2.08e+00)
INFO 2025-07-05 18:48:14,783 train_utils.py: 271: Train Epoch: [28][ 70/120] | Batch Time: 0.83 (0.61) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 3.12e+00 (2.29e+00)
INFO 2025-07-05 18:48:20,029 train_utils.py: 271: Train Epoch: [28][ 80/120] | Batch Time: 0.40 (0.60) | Data Time: 0.01 (0.06) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 32m | Losses/train_all_loss: 1.62e-01 (2.33e+00)
INFO 2025-07-05 18:48:25,717 train_utils.py: 271: Train Epoch: [28][ 90/120] | Batch Time: 0.50 (0.60) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 2.99e+00 (2.43e+00)
INFO 2025-07-05 18:48:30,451 train_utils.py: 271: Train Epoch: [28][100/120] | Batch Time: 0.72 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 1.06e+01 (2.42e+00)
INFO 2025-07-05 18:48:35,695 train_utils.py: 271: Train Epoch: [28][110/120] | Batch Time: 0.70 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 3.27e+00 (2.35e+00)
INFO 2025-07-05 18:48:40,529 trainer.py:1010: Estimated time remaining: 00d 00h 12m
INFO 2025-07-05 18:48:40,529 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:48:40,529 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.3202300833538176, 'Losses/train_all_loss_mask': 0.03057696196580461, 'Losses/train_all_loss_dice': 1.2544514812529086, 'Losses/train_all_loss_iou': 0.4143866429842698, 'Losses/train_all_loss_class': 0.03985271556152838, 'Losses/train_all_core_loss': 2.3202300833538176, 'Trainer/where': 0.7197916666666667, 'Trainer/epoch': 28, 'Trainer/steps_train': 3480}
INFO 2025-07-05 18:48:46,885 train_utils.py: 271: Train Epoch: [29][  0/120] | Batch Time: 5.41 (5.41) | Data Time: 4.36 (4.36) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 5.59e+00 (5.59e+00)
INFO 2025-07-05 18:48:52,576 train_utils.py: 271: Train Epoch: [29][ 10/120] | Batch Time: 0.68 (1.01) | Data Time: 0.00 (0.40) | Mem (GB): 6.00 (5.55/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 4.10e+00 (3.16e+00)
INFO 2025-07-05 18:48:58,212 train_utils.py: 271: Train Epoch: [29][ 20/120] | Batch Time: 0.42 (0.80) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.48/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 4.21e-01 (3.06e+00)
INFO 2025-07-05 18:49:02,743 train_utils.py: 271: Train Epoch: [29][ 30/120] | Batch Time: 0.77 (0.69) | Data Time: 0.00 (0.14) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 7.24e+00 (2.51e+00)
INFO 2025-07-05 18:49:08,619 train_utils.py: 271: Train Epoch: [29][ 40/120] | Batch Time: 0.48 (0.66) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 1.55e-01 (2.35e+00)
INFO 2025-07-05 18:49:13,613 train_utils.py: 271: Train Epoch: [29][ 50/120] | Batch Time: 0.33 (0.63) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 1.25e-01 (2.33e+00)
INFO 2025-07-05 18:49:19,976 train_utils.py: 271: Train Epoch: [29][ 60/120] | Batch Time: 0.78 (0.63) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 33m | Losses/train_all_loss: 2.13e+00 (2.59e+00)
INFO 2025-07-05 18:49:26,570 train_utils.py: 271: Train Epoch: [29][ 70/120] | Batch Time: 0.76 (0.64) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.41/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 2.23e+00 (2.59e+00)
INFO 2025-07-05 18:49:32,876 train_utils.py: 271: Train Epoch: [29][ 80/120] | Batch Time: 0.78 (0.63) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.42/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 1.45e+00 (2.57e+00)
INFO 2025-07-05 18:49:37,842 train_utils.py: 271: Train Epoch: [29][ 90/120] | Batch Time: 0.36 (0.62) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.41/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 7.11e-01 (2.48e+00)
INFO 2025-07-05 18:49:42,822 train_utils.py: 271: Train Epoch: [29][100/120] | Batch Time: 0.40 (0.61) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.40/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 3.23e-01 (2.46e+00)
INFO 2025-07-05 18:49:47,619 train_utils.py: 271: Train Epoch: [29][110/120] | Batch Time: 0.73 (0.60) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 4.66e+00 (2.37e+00)
INFO 2025-07-05 18:49:53,428 trainer.py:1010: Estimated time remaining: 00d 00h 11m
INFO 2025-07-05 18:49:53,428 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:49:53,428 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.412531381472945, 'Losses/train_all_loss_mask': 0.032112273506936614, 'Losses/train_all_loss_dice': 1.267110742876927, 'Losses/train_all_loss_iou': 0.45101773557835256, 'Losses/train_all_loss_class': 0.05215740474130447, 'Losses/train_all_core_loss': 2.412531381472945, 'Trainer/where': 0.7447916666666667, 'Trainer/epoch': 29, 'Trainer/steps_train': 3600}
INFO 2025-07-05 18:49:59,880 train_utils.py: 271: Train Epoch: [30][  0/120] | Batch Time: 5.45 (5.45) | Data Time: 4.56 (4.56) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 2.88e-01 (2.88e-01)
INFO 2025-07-05 18:50:04,517 train_utils.py: 271: Train Epoch: [30][ 10/120] | Batch Time: 0.40 (0.92) | Data Time: 0.00 (0.42) | Mem (GB): 5.00 (5.09/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 2.99e-01 (1.75e+00)
INFO 2025-07-05 18:50:08,574 train_utils.py: 271: Train Epoch: [30][ 20/120] | Batch Time: 0.57 (0.67) | Data Time: 0.00 (0.22) | Mem (GB): 6.00 (5.10/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 1.26e+00 (1.22e+00)
INFO 2025-07-05 18:50:14,256 train_utils.py: 271: Train Epoch: [30][ 30/120] | Batch Time: 0.64 (0.64) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 1.66e+00 (1.82e+00)
INFO 2025-07-05 18:50:19,907 train_utils.py: 271: Train Epoch: [30][ 40/120] | Batch Time: 0.69 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 34m | Losses/train_all_loss: 2.34e+00 (1.90e+00)
INFO 2025-07-05 18:50:25,164 train_utils.py: 271: Train Epoch: [30][ 50/120] | Batch Time: 0.65 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.25/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 3.09e+00 (1.94e+00)
INFO 2025-07-05 18:50:30,239 train_utils.py: 271: Train Epoch: [30][ 60/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 2.76e-01 (1.98e+00)
INFO 2025-07-05 18:50:36,202 train_utils.py: 271: Train Epoch: [30][ 70/120] | Batch Time: 0.63 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 2.77e+00 (2.09e+00)
INFO 2025-07-05 18:50:41,290 train_utils.py: 271: Train Epoch: [30][ 80/120] | Batch Time: 0.40 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 5.91e-01 (2.02e+00)
INFO 2025-07-05 18:50:45,837 train_utils.py: 271: Train Epoch: [30][ 90/120] | Batch Time: 0.33 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.31/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 3.90e-01 (1.86e+00)
INFO 2025-07-05 18:50:52,048 train_utils.py: 271: Train Epoch: [30][100/120] | Batch Time: 0.41 (0.57) | Data Time: 0.01 (0.05) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 9.41e-01 (2.09e+00)
INFO 2025-07-05 18:50:57,795 train_utils.py: 271: Train Epoch: [30][110/120] | Batch Time: 0.78 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 1.15e+01 (2.15e+00)
INFO 2025-07-05 18:51:03,783 trainer.py:1010: Estimated time remaining: 00d 00h 10m
INFO 2025-07-05 18:51:03,783 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:51:03,783 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.213845412277927, 'Losses/train_all_loss_mask': 0.02725509437538432, 'Losses/train_all_loss_dice': 1.2770976188282173, 'Losses/train_all_loss_iou': 0.3693548322189599, 'Losses/train_all_loss_class': 0.022291073864077287, 'Losses/train_all_core_loss': 2.213845412277927, 'Trainer/where': 0.7697916666666667, 'Trainer/epoch': 30, 'Trainer/steps_train': 3720}
INFO 2025-07-05 18:51:10,262 train_utils.py: 271: Train Epoch: [31][  0/120] | Batch Time: 5.52 (5.52) | Data Time: 4.54 (4.54) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 1.05e+01 (1.05e+01)
INFO 2025-07-05 18:51:15,418 train_utils.py: 271: Train Epoch: [31][ 10/120] | Batch Time: 0.37 (0.97) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.18/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 8.72e-01 (3.07e+00)
INFO 2025-07-05 18:51:20,038 train_utils.py: 271: Train Epoch: [31][ 20/120] | Batch Time: 0.39 (0.73) | Data Time: 0.00 (0.22) | Mem (GB): 5.00 (5.19/6.00) | Time Elapsed: 00d 00h 35m | Losses/train_all_loss: 4.83e-01 (2.45e+00)
INFO 2025-07-05 18:51:25,372 train_utils.py: 271: Train Epoch: [31][ 30/120] | Batch Time: 0.37 (0.67) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 3.10e-01 (3.11e+00)
INFO 2025-07-05 18:51:30,810 train_utils.py: 271: Train Epoch: [31][ 40/120] | Batch Time: 0.41 (0.64) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 6.57e-01 (2.74e+00)
INFO 2025-07-05 18:51:36,152 train_utils.py: 271: Train Epoch: [31][ 50/120] | Batch Time: 0.32 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 1.74e-01 (2.62e+00)
INFO 2025-07-05 18:51:41,113 train_utils.py: 271: Train Epoch: [31][ 60/120] | Batch Time: 0.39 (0.60) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 1.58e-01 (2.50e+00)
INFO 2025-07-05 18:51:46,373 train_utils.py: 271: Train Epoch: [31][ 70/120] | Batch Time: 0.35 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 2.39e-01 (2.38e+00)
INFO 2025-07-05 18:51:51,852 train_utils.py: 271: Train Epoch: [31][ 80/120] | Batch Time: 0.73 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.28/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 4.03e+00 (2.48e+00)
INFO 2025-07-05 18:51:57,313 train_utils.py: 271: Train Epoch: [31][ 90/120] | Batch Time: 0.55 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 3.68e+00 (2.52e+00)
INFO 2025-07-05 18:52:02,659 train_utils.py: 271: Train Epoch: [31][100/120] | Batch Time: 0.71 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 2.87e+00 (2.44e+00)
INFO 2025-07-05 18:52:08,026 train_utils.py: 271: Train Epoch: [31][110/120] | Batch Time: 0.32 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 2.71e-01 (2.42e+00)
INFO 2025-07-05 18:52:13,215 trainer.py:1010: Estimated time remaining: 00d 00h 09m
INFO 2025-07-05 18:52:13,215 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:52:13,215 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.4088247349485754, 'Losses/train_all_loss_mask': 0.035318076014421725, 'Losses/train_all_loss_dice': 1.2527772945662339, 'Losses/train_all_loss_iou': 0.4334545874405497, 'Losses/train_all_loss_class': 0.016231334655579608, 'Losses/train_all_core_loss': 2.4088247349485754, 'Trainer/where': 0.7947916666666667, 'Trainer/epoch': 31, 'Trainer/steps_train': 3840}
INFO 2025-07-05 18:52:19,705 train_utils.py: 271: Train Epoch: [32][  0/120] | Batch Time: 5.55 (5.55) | Data Time: 4.46 (4.46) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 36m | Losses/train_all_loss: 4.74e+00 (4.74e+00)
INFO 2025-07-05 18:52:24,581 train_utils.py: 271: Train Epoch: [32][ 10/120] | Batch Time: 0.80 (0.95) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 6.77e+00 (2.40e+00)
INFO 2025-07-05 18:52:29,990 train_utils.py: 271: Train Epoch: [32][ 20/120] | Batch Time: 0.73 (0.75) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.72e+00 (2.36e+00)
INFO 2025-07-05 18:52:35,435 train_utils.py: 271: Train Epoch: [32][ 30/120] | Batch Time: 0.35 (0.69) | Data Time: 0.00 (0.14) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 2.62e-01 (2.32e+00)
INFO 2025-07-05 18:52:41,157 train_utils.py: 271: Train Epoch: [32][ 40/120] | Batch Time: 0.64 (0.66) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 2.74e+00 (2.59e+00)
INFO 2025-07-05 18:52:46,326 train_utils.py: 271: Train Epoch: [32][ 50/120] | Batch Time: 0.34 (0.63) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.95e-01 (2.53e+00)
INFO 2025-07-05 18:52:51,380 train_utils.py: 271: Train Epoch: [32][ 60/120] | Batch Time: 0.39 (0.61) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.11e-01 (2.45e+00)
INFO 2025-07-05 18:52:56,609 train_utils.py: 271: Train Epoch: [32][ 70/120] | Batch Time: 0.34 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.37/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 2.38e-01 (2.38e+00)
INFO 2025-07-05 18:53:02,120 train_utils.py: 271: Train Epoch: [32][ 80/120] | Batch Time: 0.75 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.26e+01 (2.37e+00)
INFO 2025-07-05 18:53:07,632 train_utils.py: 271: Train Epoch: [32][ 90/120] | Batch Time: 0.74 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.32/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 9.36e-01 (2.24e+00)
INFO 2025-07-05 18:53:12,782 train_utils.py: 271: Train Epoch: [32][100/120] | Batch Time: 0.38 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.32e-01 (2.17e+00)
INFO 2025-07-05 18:53:18,307 train_utils.py: 271: Train Epoch: [32][110/120] | Batch Time: 0.37 (0.58) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 37m | Losses/train_all_loss: 1.41e-01 (2.15e+00)
INFO 2025-07-05 18:53:23,507 trainer.py:1010: Estimated time remaining: 00d 00h 08m
INFO 2025-07-05 18:53:23,507 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:53:23,507 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.1115861578534045, 'Losses/train_all_loss_mask': 0.030693640751997008, 'Losses/train_all_loss_dice': 1.145362371702989, 'Losses/train_all_loss_iou': 0.33824528253171593, 'Losses/train_all_loss_class': 0.014105691195345571, 'Losses/train_all_core_loss': 2.1115861578534045, 'Trainer/where': 0.8197916666666666, 'Trainer/epoch': 32, 'Trainer/steps_train': 3960}
INFO 2025-07-05 18:53:29,754 train_utils.py: 271: Train Epoch: [33][  0/120] | Batch Time: 5.23 (5.23) | Data Time: 4.50 (4.50) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 5.72e-01 (5.72e-01)
INFO 2025-07-05 18:53:35,180 train_utils.py: 271: Train Epoch: [33][ 10/120] | Batch Time: 0.74 (0.97) | Data Time: 0.00 (0.41) | Mem (GB): 6.00 (5.18/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 2.78e+00 (1.65e+00)
INFO 2025-07-05 18:53:40,602 train_utils.py: 271: Train Epoch: [33][ 20/120] | Batch Time: 0.71 (0.77) | Data Time: 0.00 (0.21) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 3.96e+00 (2.10e+00)
INFO 2025-07-05 18:53:45,472 train_utils.py: 271: Train Epoch: [33][ 30/120] | Batch Time: 0.35 (0.68) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 3.50e-01 (1.78e+00)
INFO 2025-07-05 18:53:50,150 train_utils.py: 271: Train Epoch: [33][ 40/120] | Batch Time: 0.58 (0.63) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 1.28e+00 (1.56e+00)
INFO 2025-07-05 18:53:55,282 train_utils.py: 271: Train Epoch: [33][ 50/120] | Batch Time: 0.35 (0.60) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 1.54e-01 (1.93e+00)
INFO 2025-07-05 18:53:59,912 train_utils.py: 271: Train Epoch: [33][ 60/120] | Batch Time: 0.39 (0.58) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.25/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 7.91e-01 (1.88e+00)
INFO 2025-07-05 18:54:05,236 train_utils.py: 271: Train Epoch: [33][ 70/120] | Batch Time: 0.65 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 7.81e+00 (1.84e+00)
INFO 2025-07-05 18:54:10,314 train_utils.py: 271: Train Epoch: [33][ 80/120] | Batch Time: 0.39 (0.57) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 1.60e-01 (1.86e+00)
INFO 2025-07-05 18:54:15,045 train_utils.py: 271: Train Epoch: [33][ 90/120] | Batch Time: 0.66 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.26/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 1.28e+01 (1.97e+00)
INFO 2025-07-05 18:54:20,538 train_utils.py: 271: Train Epoch: [33][100/120] | Batch Time: 0.41 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 38m | Losses/train_all_loss: 1.04e-01 (2.11e+00)
INFO 2025-07-05 18:54:25,826 train_utils.py: 271: Train Epoch: [33][110/120] | Batch Time: 0.36 (0.55) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 8.09e-01 (2.03e+00)
INFO 2025-07-05 18:54:30,440 trainer.py:1010: Estimated time remaining: 00d 00h 06m
INFO 2025-07-05 18:54:30,440 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:54:30,440 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.9969795826822518, 'Losses/train_all_loss_mask': 0.02569012745564881, 'Losses/train_all_loss_dice': 1.1029690836866697, 'Losses/train_all_loss_iou': 0.3604781319426062, 'Losses/train_all_loss_class': 0.01972980897404947, 'Losses/train_all_core_loss': 1.9969795826822518, 'Trainer/where': 0.8447916666666666, 'Trainer/epoch': 33, 'Trainer/steps_train': 4080}
INFO 2025-07-05 18:54:36,778 train_utils.py: 271: Train Epoch: [34][  0/120] | Batch Time: 5.40 (5.40) | Data Time: 4.69 (4.69) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 2.02e-01 (2.02e-01)
INFO 2025-07-05 18:54:41,792 train_utils.py: 271: Train Epoch: [34][ 10/120] | Batch Time: 0.36 (0.95) | Data Time: 0.00 (0.43) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 1.02e+00 (2.38e+00)
INFO 2025-07-05 18:54:48,264 train_utils.py: 271: Train Epoch: [34][ 20/120] | Batch Time: 0.69 (0.80) | Data Time: 0.00 (0.22) | Mem (GB): 6.00 (5.43/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 2.11e+00 (2.79e+00)
INFO 2025-07-05 18:54:53,420 train_utils.py: 271: Train Epoch: [34][ 30/120] | Batch Time: 0.39 (0.71) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 5.38e-01 (2.88e+00)
INFO 2025-07-05 18:54:57,558 train_utils.py: 271: Train Epoch: [34][ 40/120] | Batch Time: 0.38 (0.64) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 1.56e-01 (2.42e+00)
INFO 2025-07-05 18:55:03,930 train_utils.py: 271: Train Epoch: [34][ 50/120] | Batch Time: 0.82 (0.64) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 2.46e+00 (2.52e+00)
INFO 2025-07-05 18:55:09,274 train_utils.py: 271: Train Epoch: [34][ 60/120] | Batch Time: 0.41 (0.62) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 7.01e-01 (2.42e+00)
INFO 2025-07-05 18:55:14,350 train_utils.py: 271: Train Epoch: [34][ 70/120] | Batch Time: 0.68 (0.61) | Data Time: 0.00 (0.07) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 3.27e+00 (2.22e+00)
INFO 2025-07-05 18:55:18,722 train_utils.py: 271: Train Epoch: [34][ 80/120] | Batch Time: 0.40 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 39m | Losses/train_all_loss: 2.06e-01 (2.06e+00)
INFO 2025-07-05 18:55:25,692 train_utils.py: 271: Train Epoch: [34][ 90/120] | Batch Time: 0.39 (0.60) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 1.17e-01 (2.05e+00)
INFO 2025-07-05 18:55:30,743 train_utils.py: 271: Train Epoch: [34][100/120] | Batch Time: 0.59 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.34/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 4.44e+00 (2.02e+00)
INFO 2025-07-05 18:55:35,195 train_utils.py: 271: Train Epoch: [34][110/120] | Batch Time: 0.53 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.32/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 2.37e+00 (1.94e+00)
INFO 2025-07-05 18:55:40,505 trainer.py:1010: Estimated time remaining: 00d 00h 05m
INFO 2025-07-05 18:55:40,505 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:55:40,505 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 1.9898862990240256, 'Losses/train_all_loss_mask': 0.02460213531352077, 'Losses/train_all_loss_dice': 1.0929826967418195, 'Losses/train_all_loss_iou': 0.39090784826354746, 'Losses/train_all_loss_class': 0.013953062606742606, 'Losses/train_all_core_loss': 1.9898862990240256, 'Trainer/where': 0.8697916666666666, 'Trainer/epoch': 34, 'Trainer/steps_train': 4200}
INFO 2025-07-05 18:55:46,980 train_utils.py: 271: Train Epoch: [35][  0/120] | Batch Time: 5.52 (5.52) | Data Time: 4.45 (4.45) | Mem (GB): 6.00 (6.00/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 2.78e+00 (2.78e+00)
INFO 2025-07-05 18:55:51,540 train_utils.py: 271: Train Epoch: [35][ 10/120] | Batch Time: 0.74 (0.92) | Data Time: 0.00 (0.40) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 7.33e+00 (1.89e+00)
INFO 2025-07-05 18:55:56,269 train_utils.py: 271: Train Epoch: [35][ 20/120] | Batch Time: 0.41 (0.71) | Data Time: 0.00 (0.21) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 3.77e-01 (1.77e+00)
INFO 2025-07-05 18:56:01,903 train_utils.py: 271: Train Epoch: [35][ 30/120] | Batch Time: 0.74 (0.66) | Data Time: 0.00 (0.14) | Mem (GB): 6.00 (5.39/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 1.22e+01 (2.37e+00)
INFO 2025-07-05 18:56:06,835 train_utils.py: 271: Train Epoch: [35][ 40/120] | Batch Time: 0.53 (0.62) | Data Time: 0.00 (0.11) | Mem (GB): 5.00 (5.29/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 1.17e+00 (2.06e+00)
INFO 2025-07-05 18:56:10,946 train_utils.py: 271: Train Epoch: [35][ 50/120] | Batch Time: 0.33 (0.58) | Data Time: 0.00 (0.09) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 2.88e-01 (1.73e+00)
INFO 2025-07-05 18:56:16,433 train_utils.py: 271: Train Epoch: [35][ 60/120] | Batch Time: 0.59 (0.57) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 1.42e+00 (1.79e+00)
INFO 2025-07-05 18:56:21,434 train_utils.py: 271: Train Epoch: [35][ 70/120] | Batch Time: 0.64 (0.56) | Data Time: 0.00 (0.06) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 40m | Losses/train_all_loss: 2.95e+00 (1.76e+00)
INFO 2025-07-05 18:56:26,243 train_utils.py: 271: Train Epoch: [35][ 80/120] | Batch Time: 0.41 (0.55) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 1.46e+00 (1.65e+00)
INFO 2025-07-05 18:56:32,176 train_utils.py: 271: Train Epoch: [35][ 90/120] | Batch Time: 0.68 (0.56) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 4.27e+00 (1.83e+00)
INFO 2025-07-05 18:56:37,941 train_utils.py: 271: Train Epoch: [35][100/120] | Batch Time: 0.71 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 3.05e+00 (2.03e+00)
INFO 2025-07-05 18:56:43,424 train_utils.py: 271: Train Epoch: [35][110/120] | Batch Time: 0.63 (0.56) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 5.96e+00 (2.11e+00)
INFO 2025-07-05 18:56:47,951 trainer.py:1010: Estimated time remaining: 00d 00h 04m
INFO 2025-07-05 18:56:47,951 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:56:47,951 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.016087741404772, 'Losses/train_all_loss_mask': 0.0260663676308468, 'Losses/train_all_loss_dice': 1.0931712662180264, 'Losses/train_all_loss_iou': 0.38820820598630235, 'Losses/train_all_loss_class': 0.01338093641705503, 'Losses/train_all_core_loss': 2.016087741404772, 'Trainer/where': 0.8947916666666667, 'Trainer/epoch': 35, 'Trainer/steps_train': 4320}
INFO 2025-07-05 18:56:54,279 train_utils.py: 271: Train Epoch: [36][  0/120] | Batch Time: 5.39 (5.39) | Data Time: 4.75 (4.75) | Mem (GB): 4.00 (4.00/4.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 3.82e-01 (3.82e-01)
INFO 2025-07-05 18:56:59,333 train_utils.py: 271: Train Epoch: [36][ 10/120] | Batch Time: 0.56 (0.95) | Data Time: 0.00 (0.43) | Mem (GB): 5.00 (5.00/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 2.14e+00 (1.19e+00)
INFO 2025-07-05 18:57:03,592 train_utils.py: 271: Train Epoch: [36][ 20/120] | Batch Time: 0.40 (0.70) | Data Time: 0.00 (0.23) | Mem (GB): 5.00 (5.05/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 1.01e-01 (1.11e+00)
INFO 2025-07-05 18:57:08,877 train_utils.py: 271: Train Epoch: [36][ 30/120] | Batch Time: 0.35 (0.64) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.16/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 4.61e-01 (1.55e+00)
INFO 2025-07-05 18:57:14,458 train_utils.py: 271: Train Epoch: [36][ 40/120] | Batch Time: 0.71 (0.62) | Data Time: 0.00 (0.12) | Mem (GB): 6.00 (5.24/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 3.41e+00 (2.10e+00)
INFO 2025-07-05 18:57:20,400 train_utils.py: 271: Train Epoch: [36][ 50/120] | Batch Time: 0.64 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.31/6.00) | Time Elapsed: 00d 00h 41m | Losses/train_all_loss: 4.58e+00 (2.22e+00)
INFO 2025-07-05 18:57:26,125 train_utils.py: 271: Train Epoch: [36][ 60/120] | Batch Time: 0.46 (0.61) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 7.81e-01 (2.36e+00)
INFO 2025-07-05 18:57:31,705 train_utils.py: 271: Train Epoch: [36][ 70/120] | Batch Time: 0.34 (0.60) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 2.39e-01 (2.42e+00)
INFO 2025-07-05 18:57:37,653 train_utils.py: 271: Train Epoch: [36][ 80/120] | Batch Time: 0.77 (0.60) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 2.06e+00 (2.74e+00)
INFO 2025-07-05 18:57:42,316 train_utils.py: 271: Train Epoch: [36][ 90/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 6.02e-01 (2.56e+00)
INFO 2025-07-05 18:57:47,160 train_utils.py: 271: Train Epoch: [36][100/120] | Batch Time: 0.69 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 3.79e+00 (2.50e+00)
INFO 2025-07-05 18:57:52,225 train_utils.py: 271: Train Epoch: [36][110/120] | Batch Time: 0.64 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.35/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 3.42e+00 (2.43e+00)
INFO 2025-07-05 18:57:56,711 trainer.py:1010: Estimated time remaining: 00d 00h 03m
INFO 2025-07-05 18:57:56,711 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:57:56,721 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.374166917676727, 'Losses/train_all_loss_mask': 0.028958919606520795, 'Losses/train_all_loss_dice': 1.2903082467615605, 'Losses/train_all_loss_iou': 0.47698995352645096, 'Losses/train_all_loss_class': 0.02769034424709389, 'Losses/train_all_core_loss': 2.374166917676727, 'Trainer/where': 0.9197916666666666, 'Trainer/epoch': 36, 'Trainer/steps_train': 4440}
INFO 2025-07-05 18:58:03,243 train_utils.py: 271: Train Epoch: [37][  0/120] | Batch Time: 5.58 (5.58) | Data Time: 4.77 (4.77) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 2.69e-01 (2.69e-01)
INFO 2025-07-05 18:58:08,996 train_utils.py: 271: Train Epoch: [37][ 10/120] | Batch Time: 0.65 (1.03) | Data Time: 0.00 (0.43) | Mem (GB): 6.00 (5.27/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 3.60e+00 (2.49e+00)
INFO 2025-07-05 18:58:14,752 train_utils.py: 271: Train Epoch: [37][ 20/120] | Batch Time: 0.41 (0.81) | Data Time: 0.00 (0.23) | Mem (GB): 5.00 (5.33/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 3.15e-01 (3.00e+00)
INFO 2025-07-05 18:58:19,256 train_utils.py: 271: Train Epoch: [37][ 30/120] | Batch Time: 0.34 (0.70) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 42m | Losses/train_all_loss: 2.49e-01 (2.73e+00)
INFO 2025-07-05 18:58:24,324 train_utils.py: 271: Train Epoch: [37][ 40/120] | Batch Time: 0.41 (0.65) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.22/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 2.15e-01 (2.69e+00)
INFO 2025-07-05 18:58:29,117 train_utils.py: 271: Train Epoch: [37][ 50/120] | Batch Time: 0.51 (0.62) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.22/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 9.03e-01 (2.28e+00)
INFO 2025-07-05 18:58:34,741 train_utils.py: 271: Train Epoch: [37][ 60/120] | Batch Time: 0.73 (0.61) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.26/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 3.15e+00 (2.40e+00)
INFO 2025-07-05 18:58:39,819 train_utils.py: 271: Train Epoch: [37][ 70/120] | Batch Time: 0.32 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.27/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 1.40e-01 (2.24e+00)
INFO 2025-07-05 18:58:44,628 train_utils.py: 271: Train Epoch: [37][ 80/120] | Batch Time: 0.57 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.26/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 8.75e-01 (2.09e+00)
INFO 2025-07-05 18:58:49,757 train_utils.py: 271: Train Epoch: [37][ 90/120] | Batch Time: 0.50 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 8.85e-01 (2.04e+00)
INFO 2025-07-05 18:58:55,562 train_utils.py: 271: Train Epoch: [37][100/120] | Batch Time: 0.37 (0.57) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.23/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 1.90e-01 (2.02e+00)
INFO 2025-07-05 18:59:01,386 train_utils.py: 271: Train Epoch: [37][110/120] | Batch Time: 0.35 (0.57) | Data Time: 0.00 (0.04) | Mem (GB): 5.00 (5.24/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 2.87e-01 (2.02e+00)
INFO 2025-07-05 18:59:06,756 trainer.py:1010: Estimated time remaining: 00d 00h 02m
INFO 2025-07-05 18:59:06,756 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 18:59:06,756 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.024998293382426, 'Losses/train_all_loss_mask': 0.030305391155707184, 'Losses/train_all_loss_dice': 1.039662183324496, 'Losses/train_all_loss_iou': 0.3631405238683025, 'Losses/train_all_loss_class': 0.016087754030725893, 'Losses/train_all_core_loss': 2.024998293382426, 'Trainer/where': 0.9447916666666666, 'Trainer/epoch': 37, 'Trainer/steps_train': 4560}
INFO 2025-07-05 18:59:13,007 train_utils.py: 271: Train Epoch: [38][  0/120] | Batch Time: 5.26 (5.26) | Data Time: 4.54 (4.54) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 3.50e-01 (3.50e-01)
INFO 2025-07-05 18:59:18,356 train_utils.py: 271: Train Epoch: [38][ 10/120] | Batch Time: 0.36 (0.96) | Data Time: 0.00 (0.41) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 43m | Losses/train_all_loss: 1.63e-01 (2.54e+00)
INFO 2025-07-05 18:59:23,974 train_utils.py: 271: Train Epoch: [38][ 20/120] | Batch Time: 0.63 (0.77) | Data Time: 0.00 (0.22) | Mem (GB): 6.00 (5.43/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 1.48e+00 (2.17e+00)
INFO 2025-07-05 18:59:28,454 train_utils.py: 271: Train Epoch: [38][ 30/120] | Batch Time: 0.36 (0.67) | Data Time: 0.00 (0.15) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 2.92e-01 (1.91e+00)
INFO 2025-07-05 18:59:34,809 train_utils.py: 271: Train Epoch: [38][ 40/120] | Batch Time: 0.70 (0.66) | Data Time: 0.00 (0.11) | Mem (GB): 6.00 (5.41/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 2.50e+00 (2.17e+00)
INFO 2025-07-05 18:59:40,667 train_utils.py: 271: Train Epoch: [38][ 50/120] | Batch Time: 0.49 (0.65) | Data Time: 0.00 (0.09) | Mem (GB): 6.00 (5.47/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 1.26e+00 (2.70e+00)
INFO 2025-07-05 18:59:46,635 train_utils.py: 271: Train Epoch: [38][ 60/120] | Batch Time: 0.71 (0.64) | Data Time: 0.00 (0.08) | Mem (GB): 6.00 (5.44/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 1.85e+00 (2.55e+00)
INFO 2025-07-05 18:59:51,944 train_utils.py: 271: Train Epoch: [38][ 70/120] | Batch Time: 0.32 (0.62) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.41/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 3.78e-01 (2.54e+00)
INFO 2025-07-05 18:59:57,760 train_utils.py: 271: Train Epoch: [38][ 80/120] | Batch Time: 0.40 (0.62) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.42/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 2.67e-01 (2.44e+00)
INFO 2025-07-05 19:00:02,056 train_utils.py: 271: Train Epoch: [38][ 90/120] | Batch Time: 0.52 (0.60) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 4.94e+00 (2.29e+00)
INFO 2025-07-05 19:00:07,749 train_utils.py: 271: Train Epoch: [38][100/120] | Batch Time: 0.67 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 2.96e+00 (2.30e+00)
INFO 2025-07-05 19:00:13,261 train_utils.py: 271: Train Epoch: [38][110/120] | Batch Time: 0.64 (0.59) | Data Time: 0.00 (0.04) | Mem (GB): 6.00 (5.38/6.00) | Time Elapsed: 00d 00h 44m | Losses/train_all_loss: 1.35e+00 (2.30e+00)
INFO 2025-07-05 19:00:18,060 trainer.py:1010: Estimated time remaining: 00d 00h 01m
INFO 2025-07-05 19:00:18,060 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 19:00:18,060 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.2851656186083953, 'Losses/train_all_loss_mask': 0.028937615168009263, 'Losses/train_all_loss_dice': 1.2765783508618673, 'Losses/train_all_loss_iou': 0.40253408133867197, 'Losses/train_all_loss_class': 0.027300894292011435, 'Losses/train_all_core_loss': 2.2851656186083953, 'Trainer/where': 0.9697916666666666, 'Trainer/epoch': 38, 'Trainer/steps_train': 4680}
INFO 2025-07-05 19:00:24,778 train_utils.py: 271: Train Epoch: [39][  0/120] | Batch Time: 5.79 (5.79) | Data Time: 5.04 (5.04) | Mem (GB): 5.00 (5.00/5.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 7.77e-02 (7.77e-02)
INFO 2025-07-05 19:00:30,405 train_utils.py: 271: Train Epoch: [39][ 10/120] | Batch Time: 0.65 (1.04) | Data Time: 0.00 (0.46) | Mem (GB): 5.00 (5.36/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.88e+00 (1.71e+00)
INFO 2025-07-05 19:00:35,592 train_utils.py: 271: Train Epoch: [39][ 20/120] | Batch Time: 0.38 (0.79) | Data Time: 0.00 (0.24) | Mem (GB): 5.00 (5.38/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.23e-01 (2.58e+00)
INFO 2025-07-05 19:00:41,543 train_utils.py: 271: Train Epoch: [39][ 30/120] | Batch Time: 0.68 (0.73) | Data Time: 0.00 (0.16) | Mem (GB): 6.00 (5.42/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 7.23e+00 (2.95e+00)
INFO 2025-07-05 19:00:46,519 train_utils.py: 271: Train Epoch: [39][ 40/120] | Batch Time: 0.38 (0.67) | Data Time: 0.00 (0.12) | Mem (GB): 5.00 (5.39/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.17e+00 (2.74e+00)
INFO 2025-07-05 19:00:52,023 train_utils.py: 271: Train Epoch: [39][ 50/120] | Batch Time: 0.37 (0.65) | Data Time: 0.00 (0.10) | Mem (GB): 5.00 (5.35/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 3.54e-01 (2.70e+00)
INFO 2025-07-05 19:00:57,058 train_utils.py: 271: Train Epoch: [39][ 60/120] | Batch Time: 0.41 (0.62) | Data Time: 0.00 (0.08) | Mem (GB): 5.00 (5.34/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.16e+00 (2.70e+00)
INFO 2025-07-05 19:01:00,904 train_utils.py: 271: Train Epoch: [39][ 70/120] | Batch Time: 0.36 (0.59) | Data Time: 0.00 (0.07) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 2.24e-01 (2.39e+00)
INFO 2025-07-05 19:01:06,608 train_utils.py: 271: Train Epoch: [39][ 80/120] | Batch Time: 0.71 (0.59) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.28/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.81e+00 (2.30e+00)
INFO 2025-07-05 19:01:11,922 train_utils.py: 271: Train Epoch: [39][ 90/120] | Batch Time: 0.54 (0.58) | Data Time: 0.00 (0.06) | Mem (GB): 5.00 (5.30/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 1.28e+00 (2.28e+00)
INFO 2025-07-05 19:01:18,074 train_utils.py: 271: Train Epoch: [39][100/120] | Batch Time: 0.56 (0.59) | Data Time: 0.00 (0.05) | Mem (GB): 6.00 (5.32/6.00) | Time Elapsed: 00d 00h 45m | Losses/train_all_loss: 5.60e+00 (2.42e+00)
INFO 2025-07-05 19:01:23,339 train_utils.py: 271: Train Epoch: [39][110/120] | Batch Time: 0.36 (0.58) | Data Time: 0.00 (0.05) | Mem (GB): 5.00 (5.32/6.00) | Time Elapsed: 00d 00h 46m | Losses/train_all_loss: 2.68e-01 (2.38e+00)
INFO 2025-07-05 19:01:28,023 trainer.py:1010: Estimated time remaining: 00d 00h 00m
INFO 2025-07-05 19:01:28,023 trainer.py: 952: Synchronizing meters
INFO 2025-07-05 19:01:28,023 trainer.py: 857: Losses and meters: {'Losses/train_all_loss': 2.300306922600915, 'Losses/train_all_loss_mask': 0.03443787767221996, 'Losses/train_all_loss_dice': 1.1800638963778813, 'Losses/train_all_loss_iou': 0.4108872431718434, 'Losses/train_all_loss_class': 0.020598253497216015, 'Losses/train_all_core_loss': 2.300306922600915, 'Trainer/where': 0.9947916666666666, 'Trainer/epoch': 39, 'Trainer/steps_train': 4800}
INFO 2025-07-07 14:19:55,794 train_utils.py: 108: MACHINE SEED: 3075
INFO 2025-07-07 14:19:55,878 train_utils.py: 154: Logging ENV_VARIABLES
INFO 2025-07-07 14:19:55,878 train_utils.py: 155: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_27324_UAENKIEAEDOEUQSQ
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_ALLOW_SOFTLINKS=false
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_ROOT=C:\ProgramData\anaconda3
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_26552_1262719628=1
EFC_26552_1592913036=1
EFC_26552_2283032206=1
EFC_26552_2775293581=1
EFC_26552_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=38202
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PROMPT=(base) $P$G
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\ProgramData\anaconda3\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.101.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_OLD_CHCP=437
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET="1"
__PSLOCKDOWNPOLICY=0

INFO 2025-07-07 14:19:55,879 trainer.py:1049: Setting up components: Model, loss, optim, meters etc.
INFO 2025-07-07 14:19:55,880 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_DAVIS_8GB.yaml/tensorboard
INFO 2025-07-07 14:19:56,556 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-07-07 14:19:56,560 trainer.py:1119: ====================
INFO 2025-07-07 14:19:56,560 trainer.py:1120: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-07-07 14:19:56,562 trainer.py:1121: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-07-07 14:19:56,564 trainer.py:1122: 	Total parameters 80.9 M
INFO 2025-07-07 14:19:56,564 trainer.py:1123: 	Trainable parameters 80.9 M
INFO 2025-07-07 14:19:56,565 trainer.py:1126: 	Non-Trainable parameters 0  
INFO 2025-07-07 14:19:56,565 trainer.py:1129: ====================
INFO 2025-07-07 14:19:56,569 trainer.py:1083: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-07-07 14:19:56,569 trainer.py: 316: Moving components to device cuda:0 and local rank 0.
INFO 2025-07-07 14:19:56,660 trainer.py: 322: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 14:58:21,890 train_utils.py: 126: MACHINE SEED: 3075
INFO 2025-09-08 14:58:21,947 train_utils.py: 200: Logging ENV_VARIABLES
INFO 2025-09-08 14:58:21,947 train_utils.py: 201: ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
BUNDLED_DEBUGPY_PATH=c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_14268_KDDDYIWYYHRZNGAM
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMMONPROGRAMFILES(X86)=C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432=C:\Program Files\Common Files
COMPUTERNAME=MSI
COMSPEC=C:\Windows\system32\cmd.exe
CONDA_DEFAULT_ENV=sam2_env_py310
CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
CONDA_PREFIX=C:\Users\<USER>\.conda\envs\sam2_env_py310
CONDA_PREFIX_1=C:\ProgramData\anaconda3
CONDA_PROMPT_MODIFIER=(sam2_env_py310) 
CONDA_PYTHON_EXE=C:\ProgramData\anaconda3\python.exe
CONDA_SHLVL=2
CUDA_MODULE_LOADING=LAZY
DRIVERDATA=C:\Windows\System32\Drivers\DriverData
EFC_12308_1262719628=1
EFC_12308_1592913036=1
EFC_12308_2283032206=1
EFC_12308_2775293581=1
EFC_12308_3789132940=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
HOMEDRIVE=C:
HOMEPATH=\Users\user
HYDRA_FULL_ERROR=1
LANG=en_US.UTF-8
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOCAL_RANK=0
LOGONSERVER=\\MSI
MASTER_ADDR=localhost
MASTER_PORT=31991
NUMBER_OF_PROCESSORS=32
ONEDRIVE=C:\Users\<USER>\OneDrive
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
PATH=C:\Users\<USER>\.conda\envs\sam2_env_py310;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\mingw-w64\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\usr\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\bin;C:\Users\<USER>\.conda\envs\sam2_env_py310\Scripts;C:\Users\<USER>\.conda\envs\sam2_env_py310\bin;C:\ProgramData\anaconda3\condabin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=b701
PROGRAMDATA=C:\ProgramData
PROGRAMFILES=C:\Program Files
PROGRAMFILES(X86)=C:\Program Files (x86)
PROGRAMW6432=C:\Program Files
PSMODULEPATH=C:\Users\<USER>\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Users\user\Codings\sam2davis\sam2;C:\Users\<USER>\Codings\sam2davis;
RANK=0
SESSIONNAME=Console
SSL_CERT_FILE=C:\Users\<USER>\.conda\envs\sam2_env_py310\Library\ssl\cacert.pem
SYSTEMDRIVE=C:
SYSTEMROOT=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TORCH_NCCL_ASYNC_ERROR_HANDLING=1
USERDOMAIN=MSI
USERDOMAIN_ROAMINGPROFILE=MSI
USERNAME=user
USERPROFILE=C:\Users\<USER>\Users\user\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\.noConfigDebugAdapterEndpoints\endpoint-8dd6f41178e7380d.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-155735ab97-sock
VSCODE_INJECTION=1
WINDIR=C:\Windows
WORLD_SIZE=1
ZES_ENABLE_SYSMAN=1
_CONDA_EXE=C:\ProgramData\anaconda3\Scripts\conda.exe
_CONDA_ROOT=C:\ProgramData\anaconda3
__CONDA_OPENSLL_CERT_FILE_SET=1
__PSLOCKDOWNPOLICY=0

INFO 2025-09-08 14:58:21,949 trainer.py:1173: Setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 14:58:21,950 logger.py:  66: TensorBoard SummaryWriter instantiated. Files will be stored in: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2/configs/sam2.1_training/sam2.1_hiera_b+_DAVIS_8GB.yaml/tensorboard
INFO 2025-09-08 14:58:22,481 sam2.py:  81: Training with points (sampled from masks) as inputs with p=0.5
INFO 2025-09-08 14:58:22,483 trainer.py:1243: ====================
INFO 2025-09-08 14:58:22,483 trainer.py:1244: Summary for model <class 'training.model.sam2.SAM2Train'>
INFO 2025-09-08 14:58:22,483 trainer.py:1245: Model is SAM2Train(
  (image_encoder): ImageEncoder(
    (trunk): Hiera(
      (patch_embed): PatchEmbed(
        (proj): Conv2d(3, 112, kernel_size=(7, 7), stride=(4, 4), padding=(3, 3))
      )
      (blocks): ModuleList(
        (0): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): Identity()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (1): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=112, out_features=336, bias=True)
            (proj): Linear(in_features=112, out_features=112, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=112, out_features=448, bias=True)
              (1): Linear(in_features=448, out_features=112, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (2): MultiScaleBlock(
          (norm1): LayerNorm((112,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=112, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=112, out_features=224, bias=True)
        )
        (3-4): 2 x MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=224, out_features=672, bias=True)
            (proj): Linear(in_features=224, out_features=224, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=224, out_features=896, bias=True)
              (1): Linear(in_features=896, out_features=224, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (5): MultiScaleBlock(
          (norm1): LayerNorm((224,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=224, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=224, out_features=448, bias=True)
        )
        (6-20): 15 x MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=448, out_features=1344, bias=True)
            (proj): Linear(in_features=448, out_features=448, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=448, out_features=1792, bias=True)
              (1): Linear(in_features=1792, out_features=448, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
        (21): MultiScaleBlock(
          (norm1): LayerNorm((448,), eps=1e-06, elementwise_affine=True)
          (pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
          (attn): MultiScaleAttention(
            (q_pool): MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=0, dilation=1, ceil_mode=False)
            (qkv): Linear(in_features=448, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
          (proj): Linear(in_features=448, out_features=896, bias=True)
        )
        (22-23): 2 x MultiScaleBlock(
          (norm1): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (attn): MultiScaleAttention(
            (qkv): Linear(in_features=896, out_features=2688, bias=True)
            (proj): Linear(in_features=896, out_features=896, bias=True)
          )
          (drop_path): DropPath()
          (norm2): LayerNorm((896,), eps=1e-06, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=896, out_features=3584, bias=True)
              (1): Linear(in_features=3584, out_features=896, bias=True)
            )
            (act): GELU(approximate='none')
          )
        )
      )
    )
    (neck): FpnNeck(
      (position_encoding): PositionEmbeddingSine()
      (convs): ModuleList(
        (0): Sequential(
          (conv): Conv2d(896, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (1): Sequential(
          (conv): Conv2d(448, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (2): Sequential(
          (conv): Conv2d(224, 256, kernel_size=(1, 1), stride=(1, 1))
        )
        (3): Sequential(
          (conv): Conv2d(112, 256, kernel_size=(1, 1), stride=(1, 1))
        )
      )
    )
  )
  (mask_downsample): Conv2d(1, 1, kernel_size=(4, 4), stride=(4, 4))
  (memory_attention): MemoryAttention(
    (layers): ModuleList(
      (0-3): 4 x MemoryAttentionLayer(
        (self_attn): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=256, out_features=256, bias=True)
          (v_proj): Linear(in_features=256, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (cross_attn_image): RoPEAttention(
          (q_proj): Linear(in_features=256, out_features=256, bias=True)
          (k_proj): Linear(in_features=64, out_features=256, bias=True)
          (v_proj): Linear(in_features=64, out_features=256, bias=True)
          (out_proj): Linear(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=2048, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=2048, out_features=256, bias=True)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (memory_encoder): MemoryEncoder(
    (mask_downsampler): MaskDownSampler(
      (encoder): Sequential(
        (0): Conv2d(1, 4, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (1): LayerNorm2d()
        (2): GELU(approximate='none')
        (3): Conv2d(4, 16, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (4): LayerNorm2d()
        (5): GELU(approximate='none')
        (6): Conv2d(16, 64, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (7): LayerNorm2d()
        (8): GELU(approximate='none')
        (9): Conv2d(64, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
        (10): LayerNorm2d()
        (11): GELU(approximate='none')
        (12): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
      )
    )
    (pix_feat_proj): Conv2d(256, 256, kernel_size=(1, 1), stride=(1, 1))
    (fuser): Fuser(
      (proj): Identity()
      (layers): ModuleList(
        (0-1): 2 x CXBlock(
          (dwconv): Conv2d(256, 256, kernel_size=(7, 7), stride=(1, 1), padding=(3, 3), groups=256)
          (norm): LayerNorm2d()
          (pwconv1): Linear(in_features=256, out_features=1024, bias=True)
          (act): GELU(approximate='none')
          (pwconv2): Linear(in_features=1024, out_features=256, bias=True)
          (drop_path): Identity()
        )
      )
    )
    (position_encoding): PositionEmbeddingSine()
    (out_proj): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
  )
  (sam_prompt_encoder): PromptEncoder(
    (pe_layer): PositionEmbeddingRandom()
    (point_embeddings): ModuleList(
      (0-3): 4 x Embedding(1, 256)
    )
    (not_a_point_embed): Embedding(1, 256)
    (mask_downscaling): Sequential(
      (0): Conv2d(1, 4, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): Conv2d(4, 16, kernel_size=(2, 2), stride=(2, 2))
      (4): LayerNorm2d()
      (5): GELU(approximate='none')
      (6): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
    )
    (no_mask_embed): Embedding(1, 256)
  )
  (sam_mask_decoder): MaskDecoder(
    (transformer): TwoWayTransformer(
      (layers): ModuleList(
        (0-1): 2 x TwoWayAttentionBlock(
          (self_attn): Attention(
            (q_proj): Linear(in_features=256, out_features=256, bias=True)
            (k_proj): Linear(in_features=256, out_features=256, bias=True)
            (v_proj): Linear(in_features=256, out_features=256, bias=True)
            (out_proj): Linear(in_features=256, out_features=256, bias=True)
          )
          (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_token_to_image): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
          (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (mlp): MLP(
            (layers): ModuleList(
              (0): Linear(in_features=256, out_features=2048, bias=True)
              (1): Linear(in_features=2048, out_features=256, bias=True)
            )
            (act): ReLU()
          )
          (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (norm4): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
          (cross_attn_image_to_token): Attention(
            (q_proj): Linear(in_features=256, out_features=128, bias=True)
            (k_proj): Linear(in_features=256, out_features=128, bias=True)
            (v_proj): Linear(in_features=256, out_features=128, bias=True)
            (out_proj): Linear(in_features=128, out_features=256, bias=True)
          )
        )
      )
      (final_attn_token_to_image): Attention(
        (q_proj): Linear(in_features=256, out_features=128, bias=True)
        (k_proj): Linear(in_features=256, out_features=128, bias=True)
        (v_proj): Linear(in_features=256, out_features=128, bias=True)
        (out_proj): Linear(in_features=128, out_features=256, bias=True)
      )
      (norm_final_attn): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
    )
    (iou_token): Embedding(1, 256)
    (mask_tokens): Embedding(4, 256)
    (obj_score_token): Embedding(1, 256)
    (output_upscaling): Sequential(
      (0): ConvTranspose2d(256, 64, kernel_size=(2, 2), stride=(2, 2))
      (1): LayerNorm2d()
      (2): GELU(approximate='none')
      (3): ConvTranspose2d(64, 32, kernel_size=(2, 2), stride=(2, 2))
      (4): GELU(approximate='none')
    )
    (conv_s0): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
    (conv_s1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1))
    (output_hypernetworks_mlps): ModuleList(
      (0-3): 4 x MLP(
        (layers): ModuleList(
          (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
          (2): Linear(in_features=256, out_features=32, bias=True)
        )
        (act): ReLU()
      )
    )
    (iou_prediction_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=4, bias=True)
      )
      (act): ReLU()
    )
    (pred_obj_score_head): MLP(
      (layers): ModuleList(
        (0-1): 2 x Linear(in_features=256, out_features=256, bias=True)
        (2): Linear(in_features=256, out_features=1, bias=True)
      )
      (act): ReLU()
    )
  )
  (obj_ptr_proj): MLP(
    (layers): ModuleList(
      (0-2): 3 x Linear(in_features=256, out_features=256, bias=True)
    )
    (act): ReLU()
  )
  (obj_ptr_tpos_proj): Linear(in_features=256, out_features=64, bias=True)
)
INFO 2025-09-08 14:58:22,486 trainer.py:1246: 	Total parameters 80.9 M
INFO 2025-09-08 14:58:22,487 trainer.py:1247: 	Trainable parameters 80.9 M
INFO 2025-09-08 14:58:22,487 trainer.py:1250: 	Non-Trainable parameters 0  
INFO 2025-09-08 14:58:22,487 trainer.py:1253: ====================
INFO 2025-09-08 14:58:22,493 trainer.py:1207: Finished setting up components: Model, loss, optim, meters etc.
INFO 2025-09-08 14:58:22,493 trainer.py: 369: Moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 14:58:22,561 trainer.py: 375: Done moving components to device cuda:0 and local rank 0.
INFO 2025-09-08 14:58:22,582 optimizer.py: 248: Matches for param_name [image_encoder.*]: {'image_encoder.trunk.blocks.4.attn.proj.weight', 'image_encoder.trunk.blocks.17.attn.proj.weight', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.15.attn.qkv.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.weight', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.12.attn.proj.weight', 'image_encoder.trunk.blocks.0.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.attn.proj.weight', 'image_encoder.trunk.blocks.2.attn.proj.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.3.attn.proj.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.23.attn.proj.weight', 'image_encoder.trunk.blocks.7.attn.proj.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.17.attn.qkv.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.weight', 'image_encoder.trunk.blocks.6.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.weight', 'image_encoder.neck.convs.2.conv.weight', 'image_encoder.trunk.blocks.14.mlp.layers.0.weight', 'image_encoder.trunk.blocks.9.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'image_encoder.trunk.blocks.2.mlp.layers.0.weight', 'image_encoder.trunk.blocks.6.norm1.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.weight', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'image_encoder.trunk.blocks.1.attn.qkv.weight', 'image_encoder.trunk.blocks.5.attn.qkv.weight', 'image_encoder.trunk.blocks.21.proj.weight', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.weight', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.weight', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.4.attn.qkv.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.23.mlp.layers.1.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.16.attn.proj.weight', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.weight', 'image_encoder.trunk.blocks.14.attn.qkv.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.weight', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm2.weight', 'image_encoder.neck.convs.0.conv.bias', 'image_encoder.trunk.blocks.7.attn.qkv.weight', 'image_encoder.trunk.blocks.12.attn.qkv.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.10.attn.qkv.weight', 'image_encoder.trunk.blocks.10.attn.proj.weight', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.attn.proj.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.2.proj.weight', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.13.attn.proj.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.weight', 'image_encoder.neck.convs.0.conv.weight', 'image_encoder.trunk.blocks.1.attn.proj.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.18.attn.proj.weight', 'image_encoder.trunk.blocks.14.attn.proj.weight', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.proj.weight', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.attn.proj.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.weight', 'image_encoder.trunk.blocks.20.mlp.layers.1.weight', 'image_encoder.trunk.pos_embed_window', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.2.proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.weight', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.weight', 'image_encoder.trunk.blocks.17.norm1.bias', 'image_encoder.trunk.pos_embed', 'image_encoder.trunk.blocks.6.mlp.layers.1.weight', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'image_encoder.trunk.blocks.6.attn.proj.weight', 'image_encoder.trunk.blocks.0.attn.qkv.weight', 'image_encoder.trunk.blocks.9.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.weight', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'image_encoder.trunk.blocks.3.attn.qkv.weight', 'image_encoder.trunk.blocks.8.attn.proj.weight', 'image_encoder.trunk.blocks.18.attn.qkv.weight', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.attn.qkv.weight', 'image_encoder.trunk.blocks.11.attn.proj.weight', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.weight', 'image_encoder.trunk.blocks.18.norm2.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.weight', 'image_encoder.trunk.blocks.9.attn.qkv.weight', 'image_encoder.trunk.blocks.21.attn.qkv.weight', 'image_encoder.trunk.blocks.9.attn.proj.weight', 'image_encoder.trunk.blocks.22.mlp.layers.1.weight', 'image_encoder.trunk.blocks.6.attn.qkv.weight', 'image_encoder.trunk.blocks.15.mlp.layers.1.weight', 'image_encoder.trunk.blocks.2.mlp.layers.1.weight', 'image_encoder.trunk.blocks.7.mlp.layers.1.weight', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.1.mlp.layers.1.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.23.attn.qkv.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.weight', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'image_encoder.trunk.blocks.22.attn.proj.weight', 'image_encoder.neck.convs.3.conv.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.weight', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.weight', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'image_encoder.neck.convs.1.conv.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.weight', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'image_encoder.trunk.blocks.4.mlp.layers.0.weight', 'image_encoder.trunk.blocks.8.attn.qkv.weight', 'image_encoder.trunk.blocks.19.attn.qkv.weight', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'image_encoder.trunk.blocks.20.attn.qkv.weight', 'image_encoder.trunk.blocks.5.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.weight', 'image_encoder.trunk.patch_embed.proj.weight', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.weight', 'image_encoder.trunk.blocks.5.proj.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'image_encoder.trunk.blocks.3.norm1.weight', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.proj.weight', 'image_encoder.trunk.blocks.9.norm2.bias', 'image_encoder.trunk.blocks.5.proj.weight', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.weight', 'image_encoder.trunk.blocks.3.mlp.layers.0.weight', 'image_encoder.trunk.blocks.1.mlp.layers.0.weight', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.neck.convs.3.conv.weight', 'image_encoder.trunk.blocks.16.mlp.layers.1.weight', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.neck.convs.1.conv.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.weight', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.blocks.16.attn.qkv.weight', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.weight', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'image_encoder.trunk.blocks.13.norm2.weight', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias'}
INFO 2025-09-08 14:58:22,584 optimizer.py: 248: Matches for param_name [*bias*]: {'memory_attention.layers.3.norm2.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.2.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.0.bias', 'memory_attention.layers.3.norm1.bias', 'image_encoder.trunk.blocks.7.attn.qkv.bias', 'image_encoder.trunk.blocks.16.mlp.layers.0.bias', 'image_encoder.trunk.blocks.9.mlp.layers.1.bias', 'image_encoder.trunk.blocks.12.mlp.layers.0.bias', 'image_encoder.trunk.blocks.2.attn.proj.bias', 'memory_attention.layers.2.linear1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.0.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.18.attn.proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.1.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'image_encoder.trunk.blocks.17.norm2.bias', 'image_encoder.trunk.blocks.20.mlp.layers.1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.1.bias', 'image_encoder.trunk.blocks.11.norm1.bias', 'image_encoder.trunk.blocks.12.attn.qkv.bias', 'memory_attention.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.8.attn.proj.bias', 'image_encoder.trunk.blocks.8.mlp.layers.0.bias', 'memory_attention.layers.2.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.22.norm2.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.19.attn.proj.bias', 'obj_ptr_proj.layers.0.bias', 'image_encoder.trunk.blocks.18.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.0.linear1.bias', 'memory_attention.layers.0.norm2.bias', 'memory_attention.layers.3.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.10.mlp.layers.1.bias', 'memory_attention.layers.3.norm3.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.v_proj.bias', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.k_proj.bias', 'image_encoder.trunk.blocks.11.attn.proj.bias', 'memory_encoder.mask_downsampler.encoder.12.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.18.mlp.layers.0.bias', 'image_encoder.trunk.blocks.21.mlp.layers.0.bias', 'memory_attention.layers.0.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'memory_encoder.mask_downsampler.encoder.0.bias', 'image_encoder.trunk.blocks.2.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.4.attn.proj.bias', 'image_encoder.trunk.blocks.20.attn.proj.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'image_encoder.trunk.blocks.16.attn.proj.bias', 'memory_attention.layers.1.linear1.bias', 'sam_prompt_encoder.mask_downscaling.4.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.out_proj.bias', 'memory_attention.layers.2.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.21.attn.proj.bias', 'sam_prompt_encoder.mask_downscaling.0.bias', 'memory_attention.layers.0.linear2.bias', 'memory_attention.layers.2.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.k_proj.bias', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'image_encoder.trunk.blocks.11.mlp.layers.0.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.2.bias', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.9.attn.qkv.bias', 'image_encoder.trunk.blocks.8.norm2.bias', 'image_encoder.trunk.blocks.3.attn.qkv.bias', 'image_encoder.neck.convs.0.conv.bias', 'memory_attention.layers.2.norm2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.0.bias', 'mask_downsample.bias', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'memory_encoder.mask_downsampler.encoder.9.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.1.bias', 'memory_attention.layers.1.self_attn.k_proj.bias', 'memory_encoder.mask_downsampler.encoder.7.bias', 'image_encoder.trunk.blocks.18.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.1.bias', 'image_encoder.trunk.blocks.6.norm2.bias', 'obj_ptr_proj.layers.2.bias', 'image_encoder.trunk.blocks.3.mlp.layers.0.bias', 'memory_attention.layers.1.self_attn.out_proj.bias', 'memory_attention.layers.3.self_attn.k_proj.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.8.mlp.layers.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.v_proj.bias', 'image_encoder.trunk.blocks.7.mlp.layers.0.bias', 'memory_attention.layers.0.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.22.mlp.layers.1.bias', 'image_encoder.trunk.blocks.18.norm1.bias', 'obj_ptr_tpos_proj.bias', 'memory_encoder.fuser.layers.1.norm.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.0.bias', 'image_encoder.trunk.blocks.2.proj.bias', 'memory_encoder.fuser.layers.1.dwconv.bias', 'sam_mask_decoder.output_hypernetworks_mlps.0.layers.2.bias', 'image_encoder.trunk.blocks.4.mlp.layers.1.bias', 'memory_attention.layers.0.cross_attn_image.out_proj.bias', 'sam_mask_decoder.output_hypernetworks_mlps.2.layers.0.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.6.attn.qkv.bias', 'sam_mask_decoder.pred_obj_score_head.layers.1.bias', 'sam_mask_decoder.pred_obj_score_head.layers.0.bias', 'image_encoder.trunk.blocks.0.attn.qkv.bias', 'image_encoder.trunk.blocks.6.attn.proj.bias', 'image_encoder.trunk.blocks.2.norm2.bias', 'memory_encoder.fuser.layers.0.pwconv2.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'memory_attention.layers.3.linear1.bias', 'sam_mask_decoder.transformer.layers.1.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.10.attn.qkv.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'sam_mask_decoder.output_upscaling.1.bias', 'sam_mask_decoder.output_hypernetworks_mlps.3.layers.2.bias', 'memory_attention.layers.1.cross_attn_image.k_proj.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.q_proj.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.12.mlp.layers.1.bias', 'memory_attention.layers.2.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.0.norm1.bias', 'memory_attention.layers.3.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.11.mlp.layers.1.bias', 'memory_encoder.pix_feat_proj.bias', 'image_encoder.trunk.blocks.9.attn.proj.bias', 'sam_mask_decoder.conv_s1.bias', 'image_encoder.trunk.blocks.21.proj.bias', 'image_encoder.trunk.blocks.12.norm2.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.k_proj.bias', 'sam_mask_decoder.output_upscaling.3.bias', 'sam_mask_decoder.output_hypernetworks_mlps.1.layers.0.bias', 'memory_encoder.fuser.layers.1.pwconv1.bias', 'sam_mask_decoder.pred_obj_score_head.layers.2.bias', 'sam_mask_decoder.iou_prediction_head.layers.2.bias', 'image_encoder.trunk.blocks.5.mlp.layers.1.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.5.attn.proj.bias', 'memory_attention.layers.2.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.0.bias', 'image_encoder.trunk.blocks.22.attn.qkv.bias', 'image_encoder.trunk.blocks.14.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.4.bias', 'image_encoder.trunk.blocks.17.mlp.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.6.bias', 'memory_attention.layers.1.cross_attn_image.q_proj.bias', 'sam_prompt_encoder.mask_downscaling.1.bias', 'image_encoder.trunk.blocks.2.mlp.layers.1.bias', 'memory_attention.layers.3.cross_attn_image.q_proj.bias', 'image_encoder.trunk.blocks.23.mlp.layers.1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.23.attn.proj.bias', 'image_encoder.trunk.blocks.1.norm1.bias', 'image_encoder.trunk.blocks.4.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.v_proj.bias', 'memory_attention.layers.1.cross_attn_image.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.v_proj.bias', 'memory_attention.layers.0.norm1.bias', 'memory_encoder.mask_downsampler.encoder.3.bias', 'memory_attention.layers.2.cross_attn_image.k_proj.bias', 'sam_mask_decoder.output_upscaling.0.bias', 'image_encoder.trunk.blocks.4.attn.qkv.bias', 'image_encoder.trunk.blocks.21.norm1.bias', 'memory_attention.layers.2.cross_attn_image.out_proj.bias', 'image_encoder.trunk.blocks.13.norm1.bias', 'memory_attention.layers.0.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.14.attn.proj.bias', 'image_encoder.trunk.blocks.21.attn.qkv.bias', 'image_encoder.trunk.blocks.16.norm2.bias', 'image_encoder.trunk.blocks.15.attn.proj.bias', 'memory_attention.layers.3.cross_attn_image.v_proj.bias', 'memory_attention.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.13.attn.proj.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.13.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.v_proj.bias', 'image_encoder.trunk.blocks.1.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.out_proj.bias', 'memory_encoder.out_proj.bias', 'image_encoder.trunk.blocks.1.mlp.layers.1.bias', 'sam_prompt_encoder.mask_downscaling.6.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.2.attn.qkv.bias', 'memory_encoder.mask_downsampler.encoder.1.bias', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'memory_attention.layers.0.self_attn.out_proj.bias', 'image_encoder.neck.convs.3.conv.bias', 'sam_mask_decoder.transformer.layers.0.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.mlp.layers.1.bias', 'image_encoder.trunk.blocks.19.mlp.layers.0.bias', 'image_encoder.trunk.blocks.13.mlp.layers.1.bias', 'image_encoder.trunk.blocks.22.mlp.layers.0.bias', 'memory_encoder.fuser.layers.0.norm.bias', 'sam_prompt_encoder.mask_downscaling.3.bias', 'image_encoder.trunk.blocks.9.mlp.layers.0.bias', 'image_encoder.trunk.blocks.1.mlp.layers.0.bias', 'image_encoder.trunk.blocks.10.mlp.layers.0.bias', 'memory_encoder.fuser.layers.1.pwconv2.bias', 'memory_attention.layers.1.cross_attn_image.v_proj.bias', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.19.attn.qkv.bias', 'memory_attention.layers.1.linear2.bias', 'memory_attention.layers.3.cross_attn_image.k_proj.bias', 'memory_attention.layers.0.self_attn.k_proj.bias', 'memory_attention.layers.3.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.17.mlp.layers.1.bias', 'image_encoder.trunk.blocks.15.attn.qkv.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'memory_attention.layers.0.cross_attn_image.q_proj.bias', 'sam_mask_decoder.transformer.layers.1.self_attn.out_proj.bias', 'image_encoder.trunk.blocks.3.attn.proj.bias', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.21.mlp.layers.1.bias', 'image_encoder.trunk.blocks.20.mlp.layers.0.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'sam_mask_decoder.transformer.final_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.7.attn.proj.bias', 'memory_encoder.fuser.layers.0.dwconv.bias', 'image_encoder.neck.convs.2.conv.bias', 'image_encoder.trunk.blocks.5.attn.qkv.bias', 'image_encoder.trunk.blocks.5.proj.bias', 'memory_attention.layers.3.linear2.bias', 'image_encoder.trunk.blocks.23.attn.qkv.bias', 'image_encoder.trunk.blocks.23.norm2.bias', 'image_encoder.trunk.blocks.17.attn.qkv.bias', 'image_encoder.trunk.blocks.10.attn.proj.bias', 'image_encoder.trunk.blocks.15.mlp.layers.0.bias', 'sam_mask_decoder.iou_prediction_head.layers.1.bias', 'image_encoder.trunk.blocks.0.mlp.layers.1.bias', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.3.mlp.layers.1.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.2.norm3.bias', 'image_encoder.trunk.blocks.8.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'memory_attention.layers.2.self_attn.out_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.q_proj.bias', 'image_encoder.trunk.blocks.16.norm1.bias', 'sam_mask_decoder.transformer.layers.0.self_attn.v_proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_token_to_image.k_proj.bias', 'image_encoder.trunk.blocks.11.attn.qkv.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'image_encoder.trunk.blocks.22.attn.proj.bias', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.7.mlp.layers.1.bias', 'image_encoder.trunk.blocks.16.mlp.layers.1.bias', 'memory_attention.layers.1.self_attn.q_proj.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.1.attn.proj.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.k_proj.bias', 'obj_ptr_proj.layers.1.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'image_encoder.trunk.blocks.12.attn.proj.bias', 'image_encoder.trunk.blocks.14.mlp.layers.1.bias', 'memory_encoder.fuser.layers.0.pwconv1.bias', 'image_encoder.trunk.blocks.6.mlp.layers.1.bias', 'sam_mask_decoder.iou_prediction_head.layers.0.bias', 'memory_encoder.mask_downsampler.encoder.10.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'memory_attention.layers.3.self_attn.out_proj.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.19.mlp.layers.1.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.14.mlp.layers.0.bias', 'image_encoder.neck.convs.1.conv.bias', 'memory_attention.layers.2.linear2.bias', 'sam_mask_decoder.transformer.layers.0.cross_attn_image_to_token.q_proj.bias', 'image_encoder.trunk.blocks.16.attn.qkv.bias', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.transformer.layers.1.cross_attn_token_to_image.out_proj.bias', 'image_encoder.trunk.blocks.13.attn.qkv.bias', 'image_encoder.trunk.patch_embed.proj.bias', 'image_encoder.trunk.blocks.0.attn.proj.bias', 'sam_mask_decoder.conv_s0.bias', 'image_encoder.trunk.blocks.20.attn.qkv.bias', 'image_encoder.trunk.blocks.17.attn.proj.bias'}
INFO 2025-09-08 14:58:22,584 optimizer.py: 220: Matches for module_cls_name [torch.nn.LayerNorm]: {'memory_attention.layers.3.norm2.bias', 'memory_attention.layers.3.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm4.bias', 'image_encoder.trunk.blocks.11.norm2.weight', 'image_encoder.trunk.blocks.21.norm2.bias', 'image_encoder.trunk.blocks.19.norm2.bias', 'memory_attention.layers.3.norm2.weight', 'sam_mask_decoder.transformer.layers.0.norm2.weight', 'image_encoder.trunk.blocks.17.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm4.weight', 'image_encoder.trunk.blocks.23.norm2.weight', 'image_encoder.trunk.blocks.19.norm2.weight', 'image_encoder.trunk.blocks.11.norm1.bias', 'memory_attention.layers.3.norm3.weight', 'image_encoder.trunk.blocks.10.norm1.weight', 'image_encoder.trunk.blocks.22.norm2.bias', 'image_encoder.trunk.blocks.12.norm2.weight', 'memory_attention.layers.0.norm2.bias', 'image_encoder.trunk.blocks.3.norm2.weight', 'memory_attention.layers.3.norm3.bias', 'image_encoder.trunk.blocks.6.norm1.weight', 'sam_mask_decoder.transformer.norm_final_attn.bias', 'memory_attention.layers.1.norm2.bias', 'image_encoder.trunk.blocks.9.norm1.bias', 'image_encoder.trunk.blocks.3.norm2.bias', 'image_encoder.trunk.blocks.8.norm1.bias', 'image_encoder.trunk.blocks.15.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.bias', 'memory_attention.layers.1.norm3.bias', 'image_encoder.trunk.blocks.2.norm1.weight', 'image_encoder.trunk.blocks.2.norm2.weight', 'image_encoder.trunk.blocks.4.norm2.weight', 'image_encoder.trunk.blocks.7.norm1.weight', 'image_encoder.trunk.blocks.4.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.bias', 'image_encoder.trunk.blocks.3.norm1.bias', 'image_encoder.trunk.blocks.18.norm2.bias', 'sam_mask_decoder.transformer.norm_final_attn.weight', 'sam_mask_decoder.transformer.layers.1.norm4.bias', 'image_encoder.trunk.blocks.9.norm2.weight', 'image_encoder.trunk.blocks.8.norm2.bias', 'memory_attention.layers.0.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.weight', 'memory_attention.layers.2.norm2.bias', 'memory_attention.norm.weight', 'sam_mask_decoder.transformer.layers.1.norm1.weight', 'image_encoder.trunk.blocks.0.norm2.bias', 'image_encoder.trunk.blocks.14.norm1.bias', 'image_encoder.trunk.blocks.2.norm1.bias', 'image_encoder.trunk.blocks.16.norm1.weight', 'image_encoder.trunk.blocks.6.norm2.bias', 'image_encoder.trunk.blocks.12.norm1.weight', 'image_encoder.trunk.blocks.14.norm1.weight', 'memory_attention.layers.1.norm3.weight', 'image_encoder.trunk.blocks.10.norm2.weight', 'image_encoder.trunk.blocks.18.norm1.weight', 'image_encoder.trunk.blocks.18.norm1.bias', 'memory_attention.layers.1.norm1.weight', 'image_encoder.trunk.blocks.15.norm2.weight', 'image_encoder.trunk.blocks.14.norm2.weight', 'image_encoder.trunk.blocks.22.norm1.bias', 'image_encoder.trunk.blocks.5.norm2.weight', 'image_encoder.trunk.blocks.2.norm2.bias', 'image_encoder.trunk.blocks.17.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.bias', 'image_encoder.trunk.blocks.4.norm2.bias', 'sam_mask_decoder.transformer.layers.0.norm3.weight', 'image_encoder.trunk.blocks.0.norm1.bias', 'image_encoder.trunk.blocks.0.norm1.weight', 'image_encoder.trunk.blocks.19.norm1.weight', 'image_encoder.trunk.blocks.5.norm1.weight', 'image_encoder.trunk.blocks.12.norm2.bias', 'image_encoder.trunk.blocks.10.norm1.bias', 'image_encoder.trunk.blocks.18.norm2.weight', 'memory_attention.layers.2.norm3.weight', 'image_encoder.trunk.blocks.22.norm2.weight', 'image_encoder.trunk.blocks.13.norm1.weight', 'image_encoder.trunk.blocks.1.norm1.bias', 'memory_attention.layers.0.norm1.bias', 'image_encoder.trunk.blocks.17.norm2.weight', 'image_encoder.trunk.blocks.20.norm1.weight', 'image_encoder.trunk.blocks.11.norm1.weight', 'sam_mask_decoder.transformer.layers.1.norm3.weight', 'image_encoder.trunk.blocks.21.norm1.bias', 'image_encoder.trunk.blocks.17.norm1.weight', 'image_encoder.trunk.blocks.0.norm2.weight', 'image_encoder.trunk.blocks.13.norm1.bias', 'image_encoder.trunk.blocks.21.norm1.weight', 'image_encoder.trunk.blocks.22.norm1.weight', 'image_encoder.trunk.blocks.16.norm2.bias', 'memory_attention.layers.1.norm1.bias', 'image_encoder.trunk.blocks.13.norm2.bias', 'image_encoder.trunk.blocks.19.norm1.bias', 'image_encoder.trunk.blocks.5.norm1.bias', 'image_encoder.trunk.blocks.1.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm3.bias', 'image_encoder.trunk.blocks.8.norm1.weight', 'memory_attention.layers.0.norm3.weight', 'memory_attention.layers.3.norm1.weight', 'image_encoder.trunk.blocks.8.norm2.weight', 'image_encoder.trunk.blocks.10.norm2.bias', 'image_encoder.trunk.blocks.7.norm2.bias', 'image_encoder.trunk.blocks.23.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm4.weight', 'image_encoder.trunk.blocks.7.norm1.bias', 'image_encoder.trunk.blocks.5.norm2.bias', 'memory_attention.layers.1.norm2.weight', 'image_encoder.trunk.blocks.9.norm1.weight', 'sam_mask_decoder.transformer.layers.0.norm2.bias', 'image_encoder.trunk.blocks.15.norm1.weight', 'image_encoder.trunk.blocks.20.norm2.weight', 'image_encoder.trunk.blocks.1.norm2.weight', 'image_encoder.trunk.blocks.23.norm2.bias', 'memory_attention.layers.2.norm2.weight', 'image_encoder.trunk.blocks.3.norm1.weight', 'memory_attention.layers.0.norm3.bias', 'image_encoder.trunk.blocks.9.norm2.bias', 'sam_mask_decoder.transformer.layers.1.norm3.bias', 'memory_attention.layers.2.norm3.bias', 'sam_mask_decoder.transformer.layers.0.norm1.bias', 'memory_attention.layers.2.norm1.weight', 'image_encoder.trunk.blocks.16.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm1.bias', 'sam_mask_decoder.transformer.layers.0.norm1.weight', 'memory_attention.layers.2.norm1.bias', 'image_encoder.trunk.blocks.11.norm2.bias', 'image_encoder.trunk.blocks.6.norm1.bias', 'memory_attention.norm.bias', 'image_encoder.trunk.blocks.1.norm2.bias', 'image_encoder.trunk.blocks.4.norm1.bias', 'image_encoder.trunk.blocks.14.norm2.bias', 'image_encoder.trunk.blocks.20.norm1.bias', 'image_encoder.trunk.blocks.21.norm2.weight', 'image_encoder.trunk.blocks.7.norm2.weight', 'image_encoder.trunk.blocks.6.norm2.weight', 'image_encoder.trunk.blocks.12.norm1.bias', 'sam_mask_decoder.transformer.layers.1.norm2.weight', 'memory_attention.layers.0.norm2.weight', 'image_encoder.trunk.blocks.13.norm2.weight'} 
INFO 2025-09-08 14:58:23,949 sam2_datasets.py: 125: Dataset mixing probabilities: [1.0]
INFO 2025-09-08 14:58:23,950 trainer.py: 230: Loading checkpoint...
INFO 2025-09-08 14:58:23,950 train_utils.py: 343: Found checkpoint file: C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_DAVIS_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 14:58:23,950 trainer.py: 524: Resuming training from C:\Users\<USER>\Codings\sam2davis\sam2\sam2_logs\sam2\configs\sam2.1_training\sam2.1_hiera_b+_DAVIS_8GB.yaml\checkpoints\checkpoint.pt
INFO 2025-09-08 14:58:23,950 trainer.py: 527: Loading checkpoint file...
INFO 2025-09-08 14:58:24,603 trainer.py: 530: Checkpoint file loaded successfully
INFO 2025-09-08 14:58:24,603 trainer.py: 532: Loading model state dict...
INFO 2025-09-08 14:58:24,661 trainer.py: 538: Model state dict loaded successfully
INFO 2025-09-08 14:58:24,661 trainer.py: 540: Loading optimizer state dict...
INFO 2025-09-08 14:58:24,802 trainer.py: 542: Optimizer state dict loaded successfully
INFO 2025-09-08 14:58:24,802 trainer.py: 544: Loading loss state dict...
INFO 2025-09-08 14:58:24,802 trainer.py: 546: Loss state dict loaded successfully
INFO 2025-09-08 14:58:24,802 trainer.py: 551: Resuming from epoch 40, steps {'train': 4800, 'val': 0}
INFO 2025-09-08 14:58:24,802 trainer.py: 554: Loading AMP scaler state dict...
INFO 2025-09-08 14:58:24,802 trainer.py: 556: AMP scaler state dict loaded successfully
INFO 2025-09-08 14:58:24,802 trainer.py: 565: Checkpoint loading completed successfully
INFO 2025-09-08 14:58:24,802 trainer.py: 232: Checkpoint loaded, setting up DDP...
INFO 2025-09-08 14:58:24,802 trainer.py: 328: Setting up DistributedDataParallel...
INFO 2025-09-08 14:58:24,802 trainer.py: 329: Local rank: 0, Distributed rank: 0
INFO 2025-09-08 14:58:24,802 trainer.py: 330: Accelerator: cuda, Find unused parameters: True
INFO 2025-09-08 14:58:24,802 trainer.py: 344: Applied Windows-specific DDP settings
INFO 2025-09-08 14:58:24,802 trainer.py: 346: Creating DDP with kwargs: {'device_ids': [0], 'find_unused_parameters': True, 'broadcast_buffers': False, 'bucket_cap_mb': 25, 'gradient_as_bucket_view': True}
INFO 2025-09-08 14:58:24,991 trainer.py: 348: DistributedDataParallel setup completed successfully
INFO 2025-09-08 14:58:24,991 trainer.py: 234: DDP setup completed, calling barrier...
INFO 2025-09-08 14:58:24,995 trainer.py: 254: Barrier completed successfully
INFO 2025-09-08 14:58:24,995 trainer.py: 261: Trainer initialization completed
