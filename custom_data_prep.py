#!/usr/bin/env python3
"""
Custom Dataset Preparation Script for SAM2 Training

This script organizes segmented output from video_predictor_sam2_v7.py according to 
DAVIS2017 dataset structure for SAM2 training compatibility.

Features:
- Converts video prediction results to DAVIS2017 format
- Creates proper directory structure and file naming conventions
- Generates training file lists compatible with SAM2 training configs
- Supports multiple resolution formats
- Maintains object ID consistency across frames

Usage:
    python custom_data_prep.py --input_dir path/to/video_results --output_dir path/to/custom_dataset --video_name sequence_name

Requirements:
- Input directory with video prediction results (masks, JSON metadata)
- Output directory for DAVIS-formatted dataset
- Video sequence name for organization

Author: Enhanced from SAM2 video prediction workflow
"""

import os
import sys
import argparse
import json
import shutil
from pathlib import Path
import numpy as np
from PIL import Image
import cv2
from tqdm import tqdm


class CustomDatasetPreparator:
    """Prepares custom dataset in DAVIS2017 format for SAM2 training."""
    
    def __init__(self, input_dir, output_dir, video_name, resolution="480p"):
        """
        Initialize dataset preparator.

        Args:
            input_dir (str): Directory containing video prediction results
            output_dir (str): Output directory for DAVIS-formatted dataset
            video_name (str): Name of the video sequence
            resolution (str): Resolution folder name (e.g., "480p", "720p", "1080p")
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.video_name = video_name
        self.resolution = resolution

        # DAVIS2017 directory structure
        self.davis_root = self.output_dir / "CUSTOM"
        self.images_dir = self.davis_root / "JPEGImages" / resolution
        self.annotations_dir = self.davis_root / "Annotations" / resolution
        self.imagesets_dir = self.davis_root / "ImageSets"  # Added ImageSets directory
        self.sequence_images_dir = self.images_dir / video_name
        self.sequence_annotations_dir = self.annotations_dir / video_name

        # Training assets - place within the dataset structure for proper relative paths
        self.training_assets_dir = self.davis_root / "training" / "assets"

        print(f"Preparing custom dataset for: {video_name}")
        print(f"Input directory: {self.input_dir}")
        print(f"Output DAVIS root: {self.davis_root}")
        print(f"Resolution: {resolution}")
    
    def create_directory_structure(self):
        """Create DAVIS2017 directory structure."""
        directories = [
            self.davis_root,
            self.images_dir,
            self.annotations_dir,
            self.imagesets_dir,  # Added ImageSets directory
            self.sequence_images_dir,
            self.sequence_annotations_dir,
            self.training_assets_dir
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"Created directory: {directory}")
    
    def find_input_files(self):
        """Find input video frames and mask files with enhanced bedroom dataset support."""
        # Look for video frames directory - enhanced for bedroom dataset
        video_dir = None
        possible_video_dirs = [
            self.input_dir / f"{self.video_name}_frames",  # e.g., bedroom_frames
            self.input_dir / "video_dir",
            self.input_dir,
            self.input_dir / "frames"
        ]

        for possible_dir in possible_video_dirs:
            if possible_dir.exists():
                frame_files = list(possible_dir.glob("*.jpg")) + list(possible_dir.glob("*.png"))
                if frame_files:
                    video_dir = possible_dir
                    break

        if not video_dir:
            raise FileNotFoundError(f"Could not find video frames in {self.input_dir}")

        # Look for mask data directory - enhanced for bedroom dataset with PNG masks
        mask_data_dir = None
        possible_mask_dirs = [
            self.input_dir / "mask_data" / "png_masks",  # Specific for bedroom dataset
            self.input_dir / "mask_data",
            self.input_dir / "masks",
            self.input_dir / "png_masks",
            self.input_dir
        ]

        for possible_dir in possible_mask_dirs:
            if possible_dir.exists():
                # Look for both PNG and NPY mask files
                mask_files = (list(possible_dir.glob("*.png")) +
                             list(possible_dir.glob("*.npy")) +
                             list(possible_dir.glob("mask_*.npy")) +
                             list(possible_dir.glob("mask_*.png")))
                if mask_files:
                    mask_data_dir = possible_dir
                    break

        if not mask_data_dir:
            raise FileNotFoundError(f"Could not find mask data in {self.input_dir}")

        # Look for JSON metadata directory
        json_data_dir = None
        possible_json_dirs = [
            self.input_dir / "json_data",
            self.input_dir / "metadata",
            self.input_dir
        ]

        for possible_dir in possible_json_dirs:
            if possible_dir.exists():
                json_files = list(possible_dir.glob("*.json"))
                if json_files:
                    json_data_dir = possible_dir
                    break

        print(f"Found video frames in: {video_dir}")
        print(f"Found mask data in: {mask_data_dir}")
        if json_data_dir:
            print(f"Found JSON metadata in: {json_data_dir}")

        return video_dir, mask_data_dir, json_data_dir
    
    def copy_video_frames(self, video_dir):
        """Copy video frames to DAVIS JPEGImages directory."""
        frame_files = sorted(list(video_dir.glob("*.jpg")) + list(video_dir.glob("*.png")))
        
        if not frame_files:
            raise FileNotFoundError(f"No image files found in {video_dir}")
        
        print(f"Copying {len(frame_files)} video frames...")
        
        for i, frame_file in enumerate(tqdm(frame_files, desc="Copying frames")):
            # DAVIS naming convention: 00000.jpg, 00001.jpg, etc.
            frame_number = f"{i:05d}"
            target_file = self.sequence_images_dir / f"{frame_number}.jpg"
            
            # Convert to JPEG if needed
            if frame_file.suffix.lower() == '.png':
                img = Image.open(frame_file).convert('RGB')
                img.save(target_file, 'JPEG', quality=95)
            else:
                shutil.copy2(frame_file, target_file)
        
        print(f"Copied {len(frame_files)} frames to {self.sequence_images_dir}")
        return len(frame_files)
    
    def convert_masks_to_davis_format(self, mask_data_dir, json_data_dir, num_frames):
        """Convert mask files to DAVIS annotation format with enhanced PNG support."""
        print(f"Converting masks to DAVIS format...")

        # Get mask files - support both PNG and NPY formats
        png_mask_files = sorted(list(mask_data_dir.glob("*.png")))
        npy_mask_files = sorted(list(mask_data_dir.glob("mask_*.npy")))

        # Prefer PNG masks if available (for bedroom dataset)
        if png_mask_files:
            mask_files = png_mask_files
            mask_format = "png"
            print(f"Found {len(png_mask_files)} PNG mask files")
        elif npy_mask_files:
            mask_files = npy_mask_files
            mask_format = "npy"
            print(f"Found {len(npy_mask_files)} NPY mask files")
        else:
            print("Warning: No mask files found. Creating empty annotations.")
            self._create_empty_annotations(num_frames)
            return

        # If we have PNG masks, copy them directly (they're already in the right format)
        if mask_format == "png" and len(mask_files) > 0:
            print("Copying PNG masks directly...")
            for i, mask_file in enumerate(tqdm(mask_files, desc="Copying PNG masks")):
                frame_number = f"{i:05d}"
                target_file = self.sequence_annotations_dir / f"{frame_number}.png"
                shutil.copy2(mask_file, target_file)
            print(f"Copied {len(mask_files)} PNG masks to {self.sequence_annotations_dir}")
            return

        # Process NPY mask files
        for frame_idx in tqdm(range(num_frames), desc="Converting NPY masks"):
            frame_number = f"{frame_idx:05d}"

            # Find corresponding mask file
            mask_file = None
            for mf in mask_files:
                if f"_{frame_number}" in mf.stem or f"{frame_idx:05d}" in mf.stem:
                    mask_file = mf
                    break

            if mask_file and mask_file.exists():
                # Load mask
                mask_data = np.load(mask_file)

                # Convert to DAVIS format (uint8 with object IDs)
                davis_mask = self._convert_mask_to_davis(mask_data)

                # Save as PNG
                annotation_file = self.sequence_annotations_dir / f"{frame_number}.png"
                Image.fromarray(davis_mask).save(annotation_file)
            else:
                # Create empty annotation
                annotation_file = self.sequence_annotations_dir / f"{frame_number}.png"
                empty_mask = np.zeros((480, 854), dtype=np.uint8)  # Default resolution
                Image.fromarray(empty_mask).save(annotation_file)

        print(f"Converted {num_frames} mask annotations to {self.sequence_annotations_dir}")
    
    def _convert_mask_to_davis(self, mask_data):
        """Convert mask data to DAVIS annotation format."""
        if mask_data.dtype == np.uint16:
            # Already in object ID format
            davis_mask = mask_data.astype(np.uint8)
            # Clip values to valid range (0-255)
            davis_mask = np.clip(davis_mask, 0, 255)
        elif mask_data.dtype == bool:
            # Binary mask - assign object ID 1
            davis_mask = mask_data.astype(np.uint8)
        else:
            # Float mask - threshold and assign object ID 1
            davis_mask = (mask_data > 0.5).astype(np.uint8)
        
        return davis_mask
    
    def _create_empty_annotations(self, num_frames):
        """Create empty annotation files."""
        print("Creating empty annotation files...")
        
        for frame_idx in range(num_frames):
            frame_number = f"{frame_idx:05d}"
            annotation_file = self.sequence_annotations_dir / f"{frame_number}.png"
            
            # Create empty mask (background only)
            empty_mask = np.zeros((480, 854), dtype=np.uint8)
            Image.fromarray(empty_mask).save(annotation_file)
    
    def create_training_file_list(self):
        """Create training file list compatible with SAM2 training."""
        file_list_path = self.training_assets_dir / "CUSTOM_train_list.txt"

        # Write sequence name to file list
        with open(file_list_path, 'w') as f:
            f.write(f"{self.video_name}\n")

        print(f"Created training file list: {file_list_path}")

        # Also create a comprehensive file list with all sequences if multiple exist
        all_sequences = []
        if self.images_dir.exists():
            for seq_dir in self.images_dir.iterdir():
                if seq_dir.is_dir():
                    all_sequences.append(seq_dir.name)

        if len(all_sequences) > 1:
            comprehensive_list_path = self.training_assets_dir / "CUSTOM_all_sequences.txt"
            with open(comprehensive_list_path, 'w') as f:
                for seq_name in sorted(all_sequences):
                    f.write(f"{seq_name}\n")
            print(f"Created comprehensive sequence list: {comprehensive_list_path}")

    def create_imagesets_files(self):
        """Create ImageSets directory with train.txt and val.txt files."""
        print("Creating ImageSets files...")

        # Create train.txt file
        train_file = self.imagesets_dir / "train.txt"
        with open(train_file, 'w') as f:
            f.write(f"{self.video_name}\n")
        print(f"Created train.txt: {train_file}")

        # Create val.txt file
        val_file = self.imagesets_dir / "val.txt"
        with open(val_file, 'w') as f:
            f.write(f"{self.video_name}\n")
        print(f"Created val.txt: {val_file}")

        # Also create a comprehensive list if multiple sequences exist
        all_sequences = []
        if self.images_dir.exists():
            for seq_dir in self.images_dir.iterdir():
                if seq_dir.is_dir():
                    all_sequences.append(seq_dir.name)

        if len(all_sequences) > 1:
            # Create comprehensive train and val files
            train_all_file = self.imagesets_dir / "train_all.txt"
            val_all_file = self.imagesets_dir / "val_all.txt"

            with open(train_all_file, 'w') as f:
                for seq_name in sorted(all_sequences):
                    f.write(f"{seq_name}\n")

            with open(val_all_file, 'w') as f:
                for seq_name in sorted(all_sequences):
                    f.write(f"{seq_name}\n")

            print(f"Created comprehensive train_all.txt: {train_all_file}")
            print(f"Created comprehensive val_all.txt: {val_all_file}")
    
    def generate_dataset_info(self):
        """Generate dataset information and configuration."""
        info = {
            "dataset_name": "CUSTOM",
            "video_sequence": self.video_name,
            "resolution": self.resolution,
            "num_frames": len(list(self.sequence_images_dir.glob("*.jpg"))),
            "num_annotations": len(list(self.sequence_annotations_dir.glob("*.png"))),
            "davis_structure": {
                "img_folder": str(self.images_dir.relative_to(self.output_dir)),
                "gt_folder": str(self.annotations_dir.relative_to(self.output_dir)),
                "imagesets_folder": str(self.imagesets_dir.relative_to(self.output_dir)),
                "file_list_txt": str((self.training_assets_dir / "CUSTOM_train_list.txt").relative_to(self.output_dir))
            },
            "sam2_config_snippet": {
                "dataset": {
                    "img_folder": f"./sam2/sam2/datasets/CUSTOM/JPEGImages/{self.resolution}",
                    "gt_folder": f"./sam2/sam2/datasets/CUSTOM/Annotations/{self.resolution}",
                    "file_list_txt": "sam2/sam2/datasets/CUSTOM/training/assets/CUSTOM_train_list.txt"
                }
            },
            "imagesets_files": {
                "train_txt": str(self.imagesets_dir / "train.txt"),
                "val_txt": str(self.imagesets_dir / "val.txt")
            }
        }

        info_file = self.davis_root / "dataset_info.json"
        with open(info_file, 'w') as f:
            json.dump(info, f, indent=4)

        print(f"Generated dataset info: {info_file}")
        return info
    
    def prepare_dataset(self):
        """Main method to prepare the complete dataset."""
        print(f"\n=== Starting Custom Dataset Preparation ===")
        
        # Create directory structure
        self.create_directory_structure()
        
        # Find input files
        video_dir, mask_data_dir, json_data_dir = self.find_input_files()
        
        # Copy video frames
        num_frames = self.copy_video_frames(video_dir)
        
        # Convert masks to DAVIS format
        self.convert_masks_to_davis_format(mask_data_dir, json_data_dir, num_frames)
        
        # Create training file list
        self.create_training_file_list()

        # Create ImageSets files
        self.create_imagesets_files()

        # Generate dataset information
        dataset_info = self.generate_dataset_info()
        
        print(f"\n=== Dataset Preparation Complete ===")
        print(f"Dataset location: {self.davis_root}")
        print(f"Sequence: {self.video_name}")
        print(f"Frames: {dataset_info['num_frames']}")
        print(f"Annotations: {dataset_info['num_annotations']}")
        print(f"\nSAM2 Training Config:")
        print(f"  img_folder: {dataset_info['sam2_config_snippet']['dataset']['img_folder']}")
        print(f"  gt_folder: {dataset_info['sam2_config_snippet']['dataset']['gt_folder']}")
        print(f"  file_list_txt: {dataset_info['sam2_config_snippet']['dataset']['file_list_txt']}")
        
        return dataset_info


def main():
    parser = argparse.ArgumentParser(description="Prepare custom dataset for SAM2 training")
    parser.add_argument("--input_dir", required=True, help="Directory containing video prediction results")
    parser.add_argument("--output_dir", required=True, help="Output directory for DAVIS-formatted dataset")
    parser.add_argument("--video_name", help="Name of the video sequence (auto-detected from input_dir if not provided)")
    parser.add_argument("--resolution", default="480p", help="Resolution folder name (default: 480p)")
    parser.add_argument("--auto-detect", action="store_true", help="Auto-detect video name from input directory name")

    args = parser.parse_args()

    # Validate input directory
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory does not exist: {args.input_dir}")
        sys.exit(1)

    # Auto-detect video name if not provided or if auto-detect is enabled
    if not args.video_name or args.auto_detect:
        input_path = Path(args.input_dir)
        detected_name = input_path.name
        if args.video_name and args.auto_detect:
            print(f"Auto-detect enabled: Using '{detected_name}' instead of provided '{args.video_name}'")
        else:
            print(f"Auto-detecting video name from input directory: '{detected_name}'")
        args.video_name = detected_name
    
    # Create preparator and run
    preparator = CustomDatasetPreparator(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        video_name=args.video_name,
        resolution=args.resolution
    )
    
    try:
        dataset_info = preparator.prepare_dataset()
        print(f"\n✅ Custom dataset preparation completed successfully!")
        return dataset_info
    except Exception as e:
        print(f"\n❌ Error during dataset preparation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
